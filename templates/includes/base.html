{% load static tailwind_tags %}

<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="IE=EmulateIE7, IE=9"/>
    <meta name="Author" content="思而听（山东）网络科技有限公司"/>
    <meta name="description" content="{% block meta_description %}思而听（山东）网络科技有限公司是专业的网络安全服务提供商，为政府、金融、企业提供全方位安全防护解决方案。主要产品包括知行·天狩培训系统、天狩CTF竞赛平台、魔域模拟攻防平台等网络安全产品，以及应急响应、安全运营、攻防演练、青少年安全培训等专业安全服务。{% endblock %}"/>
    <meta name="keywords" content="{% block meta_keywords %}思而听,网络安全,安全培训,CTF竞赛,攻防演练,应急响应,安全运营,青少年安全培训,网络安全产品,安全服务,信息安全,网络防护{% endblock %}"/>
    <title>{% block title %}思而听（山东）网络科技有限公司{% endblock %}</title>
    {% tailwind_css %}
    {% include 'includes/css_includes.html' %}
</head>

<body>
<div class="home-container root">
    {% include 'includes/header.html' %}
    {% include 'includes/sidebar.html' %}


    {% block content %}
    {% endblock %}

    {% include 'includes/footer.html' %}
</div>

{% include 'includes/js_includes.html' %}

<script>
    (function () {
        var $containers = $('[data-animation]:not([data-animation-child]), [data-animation-container]')
        $containers.scrollAnimations()
    }())
</script>
</body>

</html>