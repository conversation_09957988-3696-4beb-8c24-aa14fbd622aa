{% extends 'includes/base.html' %}

{% load static %}

{% block title %}安全动态 - 思而听网络科技有限公司{% endblock %}

{% block content %}
    <!-- 页面头部横幅 -->
    <section class="position-relative bg-cover bg-center bg-no-repeat py-5 opacity-90"
             style="margin-top: 60px; background-image: url('{% static 'images/bgddc.webp' %}'),linear-gradient(180deg,#daebff,#f9fcff); background-size: cover; background-position: center; background-repeat: no-repeat;">
        <div class="container position-relative" style="z-index: 10;">
            <div class="text-center">
                <h1 class="display-4 fw-bold text-white mb-3">安全动态</h1>
                <p class="fs-5 text-muted">关注网络安全最新动态，洞察行业发展趋势</p>
            </div>
        </div>
    </section>

    <!-- 面包屑导航 -->
    <div class="container">
        <nav aria-label="breadcrumb" class="py-4 border-bottom border-light mb-4">
            <ol class="breadcrumb mb-0 small text-muted">
                <li class="breadcrumb-item">
                    <a href="/" class="text-muted text-decoration-none">首页</a>
                </li>
                <li class="breadcrumb-item text-dark" aria-current="page">安全动态</li>
                {% if active_category %}
                    <li class="breadcrumb-item text-dark" aria-current="page">{{ active_category_name }}</li>
                {% endif %}
            </ol>
        </nav>
    </div>

    <!-- 主要内容区域 -->
    <div class="container py-4">
        <div class="row g-4">
            <!-- 左侧文章列表 -->
            <div class="col-lg-8">
                <!-- 搜索栏 -->
                <div class="card shadow-sm mb-4">
                    <div class="card-body">
                        <form method="get" class="d-flex align-items-center">
                            <div class="me-3">
                                <!-- 搜索图标 SVG -->
                                <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <path d="M21 21L16.514 16.506L21 21ZM19 10.5C19 15.194 15.194 19 10.5 19C5.806 19 2 15.194 2 10.5C2 5.806 5.806 2 10.5 2C15.194 2 19 5.806 19 10.5Z" stroke="#6B7280" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                </svg>
                            </div>
                            <input type="text" name="q" placeholder="搜索文章..."
                                   value="{{ request.GET.q }}"
                                   class="form-control border-0 bg-transparent text-dark flex-grow-1 me-3">
                            <button type="submit" class="btn btn-link text-primary text-decoration-none fw-medium p-0">
                                搜索
                            </button>
                        </form>
                    </div>
                </div>

                <!-- 文章列表头部 -->
                <div class="d-flex justify-content-between align-items-center py-4 border-bottom border-light mb-4">
                    <div class="d-flex align-items-center">
                        <h4 class="h5 fw-bold text-dark mb-0">{{ active_category_name }}</h4>
                        <span class="ms-3 text-muted small">共 {{ paginator.count }} 篇文章</span>
                    </div>
                    <div class="d-flex align-items-center">
                        <span class="text-muted small me-2">排序：</span>
                        <a href="?{% if request.GET.q %}q={{ request.GET.q }}&{% endif %}order=latest"
                           class="text-primary text-decoration-none small me-3">最新发布</a>
                        <a href="?{% if request.GET.q %}q={{ request.GET.q }}&{% endif %}order=popular"
                           class="text-primary text-decoration-none small">最多浏览</a>
                    </div>
                </div>

                <!-- 文章列表 -->
                {% if articles %}
                    {% for article in articles %}
                        <div class="card shadow-sm mb-4 overflow-hidden">
                            {% if article.image %}
                                <div class="row g-0">
                                    <div class="col-md-4">
                                        <img src="{{ article.image.url }}"
                                             alt="{{ article.title }}"
                                             class="img-fluid w-100 h-100 object-fit-cover"
                                             style="min-height: 150px;">
                                    </div>
                                    <div class="col-md-8">
                                        <div class="card-body">
                                            <div class="d-flex align-items-center mb-3">
                                                <span class="badge bg-primary me-2">{{ article.category.name }}</span>
                                                <span class="text-muted small">{{ article.created_at|date:"Y-m-d H:i" }}</span>
                                                <span class="text-muted small ms-3">浏览 {{ article.views }}</span>
                                            </div>
                                            <h5 class="card-title h6 mb-3">
                                                <a href="{% url 'news:article_detail' article.pk %}"
                                                   class="text-dark text-decoration-none">{{ article.title }}</a>
                                            </h5>
                                            <p class="card-text text-muted small mb-3" style="display: -webkit-box; -webkit-line-clamp: 2; -webkit-box-orient: vertical; overflow: hidden;">{{ article.description }}</p>
                                            <div class="d-flex align-items-center justify-content-between">
                                                <div class="text-muted small">
                                                    关键词：{{ article.keywords|truncatechars:50 }}
                                                </div>
                                                <a href="{% url 'news:article_detail' article.pk %}"
                                                   class="text-primary text-decoration-none small fw-medium">
                                                    阅读全文
                                                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" class="ms-1">
                                                        <path d="M5 12H19M19 12L12 5M19 12L12 19" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                                    </svg>
                                                </a>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            {% else %}
                                <div class="card-body">
                                    <div class="d-flex align-items-center mb-3">
                                        <span class="badge bg-primary me-2">{{ article.category.name }}</span>
                                        <span class="text-muted small">{{ article.created_at|date:"Y-m-d H:i" }}</span>
                                        <span class="text-muted small ms-3">浏览 {{ article.views }}</span>
                                    </div>
                                    <h5 class="card-title h6 mb-3">
                                        <a href="{% url 'news:article_detail' article.pk %}"
                                           class="text-dark text-decoration-none">{{ article.title }}</a>
                                    </h5>
                                    <p class="card-text text-muted small mb-3" style="display: -webkit-box; -webkit-line-clamp: 2; -webkit-box-orient: vertical; overflow: hidden;">{{ article.description }}</p>
                                    <div class="d-flex align-items-center justify-content-between">
                                        <div class="text-muted small">
                                            关键词：{{ article.keywords|truncatechars:50 }}
                                        </div>
                                        <a href="{% url 'news:article_detail' article.pk %}"
                                           class="text-primary text-decoration-none small fw-medium">
                                            阅读全文
                                            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" class="ms-1">
                                                <path d="M5 12H19M19 12L12 5M19 12L12 19" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                            </svg>
                                        </a>
                                    </div>
                                </div>
                            {% endif %}
                        </div>
                    {% endfor %}

                    <!-- 分页导航 -->
                    {% if is_paginated %}
                        <nav aria-label="文章分页" class="mt-4">
                            <ul class="pagination justify-content-center">
                                {% if page_obj.has_previous %}
                                    <li class="page-item">
                                        <a class="page-link" href="?{% if request.GET.q %}q={{ request.GET.q }}&{% endif %}page=1">首页</a>
                                    </li>
                                    <li class="page-item">
                                        <a class="page-link" href="?{% if request.GET.q %}q={{ request.GET.q }}&{% endif %}page={{ page_obj.previous_page_number }}">上一页</a>
                                    </li>
                                {% endif %}

                                {% for num in page_obj.paginator.page_range %}
                                    {% if page_obj.number == num %}
                                        <li class="page-item active">
                                            <span class="page-link">{{ num }}</span>
                                        </li>
                                    {% elif num > page_obj.number|add:'-3' and num < page_obj.number|add:'3' %}
                                        <li class="page-item">
                                            <a class="page-link" href="?{% if request.GET.q %}q={{ request.GET.q }}&{% endif %}page={{ num }}">{{ num }}</a>
                                        </li>
                                    {% endif %}
                                {% endfor %}

                                {% if page_obj.has_next %}
                                    <li class="page-item">
                                        <a class="page-link" href="?{% if request.GET.q %}q={{ request.GET.q }}&{% endif %}page={{ page_obj.next_page_number }}">下一页</a>
                                    </li>
                                    <li class="page-item">
                                        <a class="page-link" href="?{% if request.GET.q %}q={{ request.GET.q }}&{% endif %}page={{ page_obj.paginator.num_pages }}">末页</a>
                                    </li>
                                {% endif %}
                            </ul>
                        </nav>
                    {% endif %}
                {% else %}
                    <!-- 无文章时的提示 -->
                    <div class="text-center py-5">
                        <!-- 无数据图标 SVG -->
                        <div class="mb-4">
                            <svg width="120" height="120" viewBox="0 0 120 120" fill="none" xmlns="http://www.w3.org/2000/svg" class="mx-auto">
                                <circle cx="60" cy="60" r="50" fill="#F3F4F6"/>
                                <path d="M40 50H80M40 60H70M40 70H75" stroke="#9CA3AF" stroke-width="2" stroke-linecap="round"/>
                                <circle cx="60" cy="60" r="58" stroke="#E5E7EB" stroke-width="2"/>
                            </svg>
                        </div>
                        <h5 class="h6 fw-medium text-muted mb-2">暂无相关文章</h5>
                        <p class="text-muted small mb-4">
                            {% if request.GET.q %}
                                没有找到包含"{{ request.GET.q }}"的文章，请尝试其他关键词
                            {% else %}
                                该分类下暂无文章，敬请期待
                            {% endif %}
                        </p>
                        <a href="/news" class="btn btn-primary">查看全部文章</a>
                    </div>
                {% endif %}
            </div>

            <!-- 右侧边栏 -->
            <div class="col-lg-4">
                <!-- 文章分类 -->
                <div class="card mb-4">
                    <div class="card-header bg-light">
                        <h5 class="card-title h6 mb-0 fw-bold">文章分类</h5>
                    </div>
                    <div class="card-body">
                        <div class="list-group list-group-flush">
                            <a href="/news"
                               class="list-group-item list-group-item-action border-0 {% if not active_category %}active{% endif %}">
                                全部文章
                            </a>
                            {% for category in categories %}
                                <a href="{% url 'news:category_article_list' category.id %}"
                                   class="list-group-item list-group-item-action border-0 {% if active_category == category.id %}active{% endif %}">
                                    {{ category.name }}
                                </a>
                            {% endfor %}
                        </div>
                    </div>
                </div>

                <!-- 热门文章 -->
                <div class="card mb-4">
                    <div class="card-header bg-light">
                        <h5 class="card-title h6 mb-0 fw-bold">热门文章</h5>
                    </div>
                    <div class="card-body">
                        <div class="list-group list-group-flush">
                            <div class="list-group-item border-0 border-bottom pb-3">
                                <div class="d-flex">
                                    <div class="me-3">
                                        <span class="badge bg-danger rounded-circle d-flex align-items-center justify-content-center" style="width: 24px; height: 24px; font-size: 12px;">1</span>
                                    </div>
                                    <div class="flex-grow-1">
                                        <div class="mb-1">
                                            <a href="/news/security-threats-2024"
                                               class="text-dark text-decoration-none small">2024年网络安全威胁趋势分析与防护策略</a>
                                        </div>
                                        <div class="text-muted small">浏览量：1,234</div>
                                    </div>
                                </div>
                            </div>

                            <div class="list-group-item border-0 border-bottom pb-3">
                                <div class="d-flex">
                                    <div class="me-3">
                                        <span class="badge bg-warning rounded-circle d-flex align-items-center justify-content-center" style="width: 24px; height: 24px; font-size: 12px;">2</span>
                                    </div>
                                    <div class="flex-grow-1">
                                        <div class="mb-1">
                                            <a href="/news/ai-cybersecurity"
                                               class="text-dark text-decoration-none small">AI赋能网络安全：思而听智能防护体系解析</a>
                                        </div>
                                        <div class="text-muted small">浏览量：987</div>
                                    </div>
                                </div>
                            </div>

                            <div class="list-group-item border-0 border-bottom pb-3">
                                <div class="d-flex">
                                    <div class="me-3">
                                        <span class="badge bg-success rounded-circle d-flex align-items-center justify-content-center" style="width: 24px; height: 24px; font-size: 12px;">3</span>
                                    </div>
                                    <div class="flex-grow-1">
                                        <div class="mb-1">
                                            <a href="/news/enterprise-security"
                                               class="text-dark text-decoration-none small">企业网络安全建设指南：从零基础到全面防护</a>
                                        </div>
                                        <div class="text-muted small">浏览量：756</div>
                                    </div>
                                </div>
                            </div>

                            <div class="list-group-item border-0">
                                <div class="d-flex">
                                    <div class="me-3">
                                        <span class="badge bg-primary rounded-circle d-flex align-items-center justify-content-center" style="width: 24px; height: 24px; font-size: 12px;">4</span>
                                    </div>
                                    <div class="flex-grow-1">
                                        <div class="mb-1">
                                            <a href="/news/emergency-response"
                                               class="text-dark text-decoration-none small">网络安全应急响应实战：48小时内快速恢复业务</a>
                                        </div>
                                        <div class="text-muted small">浏览量：623</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 热门标签 -->
                <div class="card mb-4">
                    <div class="card-header bg-light">
                        <h5 class="card-title h6 mb-0 fw-bold">热门标签</h5>
                    </div>
                    <div class="card-body">
                        <div class="d-flex flex-wrap gap-2">
                            <a href="/news?q=网络安全" class="badge bg-outline-primary text-primary border border-primary text-decoration-none">网络安全</a>
                            <a href="/news?q=威胁情报" class="badge bg-outline-primary text-primary border border-primary text-decoration-none">威胁情报</a>
                            <a href="/news?q=应急响应" class="badge bg-outline-primary text-primary border border-primary text-decoration-none">应急响应</a>
                            <a href="/news?q=安全培训" class="badge bg-outline-primary text-primary border border-primary text-decoration-none">安全培训</a>
                            <a href="/news?q=攻防演练" class="badge bg-outline-primary text-primary border border-primary text-decoration-none">攻防演练</a>
                            <a href="/news?q=安全运营" class="badge bg-outline-primary text-primary border border-primary text-decoration-none">安全运营</a>
                            <a href="/news?q=CTF竞赛" class="badge bg-outline-primary text-primary border border-primary text-decoration-none">CTF竞赛</a>
                            <a href="/news?q=人工智能" class="badge bg-outline-primary text-primary border border-primary text-decoration-none">人工智能</a>
                        </div>
                    </div>
                </div>

                <!-- 联系我们 -->
                <div class="card bg-primary text-white">
                    <div class="card-body">
                        <h5 class="card-title h6 fw-bold mb-3">需要专业安全服务？</h5>
                        <p class="card-text small mb-4">思而听为您提供全方位的网络安全解决方案</p>
                        <div class="mb-4">
                            <div class="d-flex align-items-center mb-2">
                                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" class="me-2">
                                    <path d="M22 16.92V19.92C22 20.52 21.52 21 20.92 21C9.4 21 0 11.6 0 0.08C0 -0.52 0.48 -1 1.08 -1H4.08C4.68 -1 5.16 -0.52 5.16 0.08C5.16 2.08 5.56 4 6.28 5.76C6.44 6.12 6.32 6.56 6 6.84L4.84 8C6.84 11.92 10.08 15.16 14 17.16L15.16 16C15.44 15.68 15.88 15.56 16.24 15.72C18 16.44 19.92 16.84 21.92 16.84C22.52 16.84 23 17.32 23 17.92V20.92Z" fill="currentColor"/>
                                </svg>
                                <span class="small">咨询热线：400-123-4567</span>
                            </div>
                            <div class="d-flex align-items-center mb-2">
                                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" class="me-2">
                                    <path d="M4 4H20C21.1 4 22 4.9 22 6V18C22 19.1 21.1 20 20 20H4C2.9 20 2 19.1 2 18V6C2 4.9 2.9 4 4 4Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                    <path d="L22 6L12 13L2 6" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                </svg>
                                <span class="small">邮箱：<EMAIL></span>
                            </div>
                            <div class="d-flex align-items-center">
                                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" class="me-2">
                                    <circle cx="12" cy="12" r="10" stroke="currentColor" stroke-width="2"/>
                                    <path d="M2 12H22" stroke="currentColor" stroke-width="2"/>
                                    <path d="M12 2C14.5 4.5 16 8.5 16 12C16 15.5 14.5 19.5 12 22C9.5 19.5 8 15.5 8 12C8 8.5 9.5 4.5 12 2Z" stroke="currentColor" stroke-width="2"/>
                                </svg>
                                <span class="small">官网：www.sierting.com</span>
                            </div>
                        </div>
                        <a href="/contact" class="btn btn-light btn-sm text-primary fw-medium">立即咨询</a>
                    </div>
                </div>
            </div>
        </div>
    </div>
{% endblock %}