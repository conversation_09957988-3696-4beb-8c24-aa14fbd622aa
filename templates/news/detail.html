{% extends 'includes/base.html' %}

{% load static %}

{% block title %}{{ article.title }} - 思而听网络科技有限公司{% endblock %}

{% block content %}
<!-- 面包屑导航 -->
<div class="container" style="margin-top: 80px;">
    <nav aria-label="breadcrumb" class="py-3">
        <ol class="breadcrumb mb-0">
            <li class="breadcrumb-item">
                <a href="/" class="text-decoration-none">首页</a>
            </li>
            <li class="breadcrumb-item">
                <a href="/news" class="text-decoration-none">安全动态</a>
            </li>
            <li class="breadcrumb-item active text-truncate" aria-current="page" style="max-width: 300px;">
                {{ article.title }}
            </li>
        </ol>
    </nav>
</div>

<!-- 主要内容区域 -->
<div class="container py-4">
    <div class="row g-4">
        <!-- 左侧文章内容 -->
        <div class="col-12 col-lg-8">
            <article class="card shadow-sm">
                <div class="card-body">
                    <!-- 文章标题 -->
                    <h1 class="display-6 fw-bold text-dark mb-4">{{ article.title }}</h1>

                    <!-- 文章信息 -->
                    <div class="d-flex flex-wrap align-items-center text-muted small mb-4 pb-3 border-bottom">
                        <div class="d-flex align-items-center me-4 mb-2">
                            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" class="me-2">
                                <circle cx="12" cy="12" r="10" stroke="currentColor" stroke-width="2"/>
                                <path d="M12 6V12L16 14" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                            </svg>
                            <span>发布时间：{{ article.created_at|date:"Y-m-d H:i" }}</span>
                        </div>
                        <div class="d-flex align-items-center me-4 mb-2">
                            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" class="me-2">
                                <path d="M4 4H20C21.1 4 22 4.9 22 6V18C22 19.1 21.1 20 20 20H4C2.9 20 2 19.1 2 18V6C2 4.9 2.9 4 4 4Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                <path d="M22 6L12 13L2 6" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                            </svg>
                            <span>分类：{{ article.category.name }}</span>
                        </div>
                        <div class="d-flex align-items-center mb-2">
                            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" class="me-2">
                                <path d="M1 12S5 4 12 4S23 12 23 12S19 20 12 20S1 12 1 12Z" stroke="currentColor" stroke-width="2"/>
                                <circle cx="12" cy="12" r="3" stroke="currentColor" stroke-width="2"/>
                            </svg>
                            <span>浏览量：{{ article.views }}</span>
                        </div>
                    </div>

                    <!-- 文章图片 -->
                    {% if article.image %}
                    <div class="mb-4">
                        <img src="{{ article.image.url }}" alt="{{ article.title }}" class="img-fluid rounded">
                    </div>
                    {% endif %}

                    <!-- 文章内容 -->
                    <div class="article-content lh-lg">
                        {{ article.content|linebreaks }}
                    </div>
                </div>
            </article>

            <!-- 分享和标签 -->
            <div class="card mt-4">
                <div class="card-body">
                    <div class="d-flex flex-column flex-md-row align-items-start justify-content-between">
                        <div class="mb-3 mb-md-0">
                            <span class="text-muted small">关键词：</span>
                            <span class="text-primary">{{ article.keywords }}</span>
                        </div>
                        <div class="d-flex align-items-center">
                            <span class="text-muted small me-3">分享到：</span>
                            <div class="d-flex gap-2">
                                <a href="#" class="btn btn-outline-success btn-sm d-flex align-items-center" title="分享到微信">
                                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" class="me-1">
                                        <path d="M8.5 12C9.32843 12 10 11.3284 10 10.5C10 9.67157 9.32843 9 8.5 9C7.67157 9 7 9.67157 7 10.5C7 11.3284 7.67157 12 8.5 12Z" fill="currentColor"/>
                                        <path d="M15.5 12C16.3284 12 17 11.3284 17 10.5C17 9.67157 16.3284 9 15.5 9C14.6716 9 14 9.67157 14 10.5C14 11.3284 14.6716 12 15.5 12Z" fill="currentColor"/>
                                        <path d="M12 2C6.48 2 2 6.48 2 12C2 17.52 6.48 22 12 22C17.52 22 22 17.52 22 12C22 6.48 17.52 2 12 2Z" stroke="currentColor" stroke-width="2"/>
                                    </svg>
                                    微信
                                </a>
                                <a href="#" class="btn btn-outline-danger btn-sm d-flex align-items-center" title="分享到微博">
                                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" class="me-1">
                                        <path d="M12 2L13.09 8.26L20 9L13.09 9.74L12 16L10.91 9.74L4 9L10.91 8.26L12 2Z" stroke="currentColor" stroke-width="2" stroke-linejoin="round"/>
                                    </svg>
                                    微博
                                </a>
                                <a href="#" class="btn btn-outline-primary btn-sm d-flex align-items-center" title="分享到QQ">
                                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" class="me-1">
                                        <circle cx="12" cy="12" r="10" stroke="currentColor" stroke-width="2"/>
                                        <path d="M8 14S9 9 12 9S16 14 16 14" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
                                    </svg>
                                    QQ
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 右侧推荐内容 -->
        <div class="col-12 col-lg-4">
            <!-- 推荐文章 -->
            <div class="card mb-4">
                <div class="card-header bg-light">
                    <h5 class="card-title h6 mb-0 fw-bold">推荐阅读</h5>
                </div>
                <div class="card-body">
                    <div class="list-group list-group-flush">
                        <div class="list-group-item border-0 border-bottom pb-3">
                            <div class="row g-3">
                                <div class="col-4">
                                    <img src="https://images.unsplash.com/photo-1550751827-4bd374c3f58b?w=300&h=200&fit=crop&crop=center"
                                         alt="网络安全威胁分析"
                                         class="img-fluid rounded">
                                </div>
                                <div class="col-8">
                                    <h6 class="mb-2">
                                        <a href="/news/security-threats-2024"
                                           class="text-dark text-decoration-none small">2024年网络安全威胁趋势分析与防护策略</a>
                                    </h6>
                                    <p class="text-muted small mb-2">深入分析当前网络安全威胁态势，提供专业防护建议...</p>
                                    <small class="text-muted">2024-01-15</small>
                                </div>
                            </div>
                        </div>

                        <div class="list-group-item border-0 border-bottom pb-3">
                            <div class="row g-3">
                                <div class="col-4">
                                    <img src="https://images.unsplash.com/photo-1677442136019-21780ecad995?w=300&h=200&fit=crop&crop=center"
                                         alt="AI网络安全"
                                         class="img-fluid rounded">
                                </div>
                                <div class="col-8">
                                    <h6 class="mb-2">
                                        <a href="/news/ai-cybersecurity"
                                           class="text-dark text-decoration-none small">AI赋能网络安全：思而听智能防护体系解析</a>
                                    </h6>
                                    <p class="text-muted small mb-2">探索人工智能在网络安全领域的应用与发展...</p>
                                    <small class="text-muted">2024-01-12</small>
                                </div>
                            </div>
                        </div>

                        <div class="list-group-item border-0 border-bottom pb-3">
                            <div class="row g-3">
                                <div class="col-4">
                                    <img src="https://images.unsplash.com/photo-1560472354-b33ff0c44a43?w=300&h=200&fit=crop&crop=center"
                                         alt="企业安全建设"
                                         class="img-fluid rounded">
                                </div>
                                <div class="col-8">
                                    <h6 class="mb-2">
                                        <a href="/news/enterprise-security"
                                           class="text-dark text-decoration-none small">企业网络安全建设指南：从零基础到全面防护</a>
                                    </h6>
                                    <p class="text-muted small mb-2">为企业提供全面的网络安全建设指导方案...</p>
                                    <small class="text-muted">2024-01-10</small>
                                </div>
                            </div>
                        </div>

                        <div class="list-group-item border-0">
                            <div class="row g-3">
                                <div class="col-4">
                                    <img src="https://images.unsplash.com/photo-1563013544-824ae1b704d3?w=300&h=200&fit=crop&crop=center"
                                         alt="应急响应"
                                         class="img-fluid rounded">
                                </div>
                                <div class="col-8">
                                    <h6 class="mb-2">
                                        <a href="/news/emergency-response"
                                           class="text-dark text-decoration-none small">网络安全应急响应实战：48小时内快速恢复业务</a>
                                    </h6>
                                    <p class="text-muted small mb-2">分享网络安全应急响应的实战经验和最佳实践...</p>
                                    <small class="text-muted">2024-01-08</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 热门标签 -->
            <div class="card mb-4">
                <div class="card-header bg-light">
                    <h5 class="card-title h6 mb-0 fw-bold">热门标签</h5>
                </div>
                <div class="card-body">
                    <div class="d-flex flex-wrap gap-2">
                        <a href="/news/tag/网络安全" class="btn btn-outline-primary btn-sm">网络安全</a>
                        <a href="/news/tag/威胁情报" class="btn btn-outline-primary btn-sm">威胁情报</a>
                        <a href="/news/tag/应急响应" class="btn btn-outline-primary btn-sm">应急响应</a>
                        <a href="/news/tag/安全培训" class="btn btn-outline-primary btn-sm">安全培训</a>
                        <a href="/news/tag/攻防演练" class="btn btn-outline-primary btn-sm">攻防演练</a>
                        <a href="/news/tag/安全运营" class="btn btn-outline-primary btn-sm">安全运营</a>
                        <a href="/news/tag/CTF竞赛" class="btn btn-outline-primary btn-sm">CTF竞赛</a>
                        <a href="/news/tag/人工智能" class="btn btn-outline-primary btn-sm">人工智能</a>
                    </div>
                </div>
            </div>

            <!-- 联系我们 -->
            <div class="card bg-primary text-white">
                <div class="card-body">
                    <h5 class="card-title h6 fw-bold mb-3">需要专业安全服务？</h5>
                    <p class="card-text small mb-3">思而听为您提供全方位的网络安全解决方案</p>
                    <div class="mb-3">
                        <div class="d-flex align-items-center mb-2">
                            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" class="me-2">
                                <path d="M22 16.92V19.92C22 20.52 21.52 21 20.92 21C9.4 21 0 11.6 0 0.08C0 -0.52 0.48 -1 1.08 -1H4.08C4.68 -1 5.16 -0.52 5.16 0.08C5.16 2.08 5.56 4 6.28 5.76C6.44 6.12 6.32 6.56 6 6.84L4.84 8C6.84 11.92 10.08 15.16 14 17.16L15.16 16C15.44 15.68 15.88 15.56 16.24 15.72C18 16.44 19.92 16.84 21.92 16.84C22.52 16.84 23 17.32 23 17.92V20.92Z" fill="currentColor"/>
                            </svg>
                            <span class="small">咨询热线：400-123-4567</span>
                        </div>
                        <div class="d-flex align-items-center mb-2">
                            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" class="me-2">
                                <path d="M4 4H20C21.1 4 22 4.9 22 6V18C22 19.1 21.1 20 20 20H4C2.9 20 2 19.1 2 18V6C2 4.9 2.9 4 4 4Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                <path d="L22 6L12 13L2 6" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                            </svg>
                            <span class="small">邮箱：<EMAIL></span>
                        </div>
                        <div class="d-flex align-items-center">
                            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" class="me-2">
                                <circle cx="12" cy="12" r="10" stroke="currentColor" stroke-width="2"/>
                                <path d="M2 12H22" stroke="currentColor" stroke-width="2"/>
                                <path d="M12 2C14.5 4.5 16 8.5 16 12C16 15.5 14.5 19.5 12 22C9.5 19.5 8 15.5 8 12C8 8.5 9.5 4.5 12 2Z" stroke="currentColor" stroke-width="2"/>
                            </svg>
                            <span class="small">官网：www.sierting.com</span>
                        </div>
                    </div>
                    <a href="/contact" class="btn btn-light btn-sm text-primary fw-medium">立即咨询</a>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 相关文章推荐 -->
<section class="bg-light py-5">
    <div class="container">
        <h3 class="h4 fw-bold text-dark text-center mb-5">相关文章推荐</h3>
        <div class="row g-4">
            <div class="col-12 col-md-6 col-lg-3">
                <div class="card h-100 border-0 shadow-sm">
                    <img src="https://images.unsplash.com/photo-1614064641938-3bbee52942c7?w=400&h=250&fit=crop&crop=center" class="card-img-top" alt="零信任架构">
                    <div class="card-body d-flex flex-column">
                        <h6 class="card-title fw-bold mb-3">零信任架构在企业安全中的应用实践</h6>
                        <p class="card-text text-muted small flex-grow-1">探讨零信任安全架构的核心理念和实施策略...</p>
                        <div class="d-flex justify-content-between align-items-center mt-auto">
                            <small class="text-muted">2024-01-05</small>
                            <a href="#" class="btn btn-outline-primary btn-sm">
                                阅读
                                <svg width="14" height="14" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" class="ms-1">
                                    <path d="M5 12H19M19 12L12 5M19 12L12 19" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                </svg>
                            </a>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-12 col-md-6 col-lg-3">
                <div class="card h-100 border-0 shadow-sm">
                    <img src="https://images.unsplash.com/photo-1451187580459-43490279c0fa?w=400&h=250&fit=crop&crop=center" class="card-img-top" alt="云安全">
                    <div class="card-body d-flex flex-column">
                        <h6 class="card-title fw-bold mb-3">云原生安全：容器化环境的安全防护策略</h6>
                        <p class="card-text text-muted small flex-grow-1">深入分析云原生环境下的安全挑战与解决方案...</p>
                        <div class="d-flex justify-content-between align-items-center mt-auto">
                            <small class="text-muted">2024-01-03</small>
                            <a href="#" class="btn btn-outline-primary btn-sm">
                                阅读
                                <svg width="14" height="14" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" class="ms-1">
                                    <path d="M5 12H19M19 12L12 5M19 12L12 19" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                </svg>
                            </a>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-12 col-md-6 col-lg-3">
                <div class="card h-100 border-0 shadow-sm">
                    <img src="https://images.unsplash.com/photo-1558494949-ef010cbdcc31?w=400&h=250&fit=crop&crop=center" class="card-img-top" alt="数据安全">
                    <div class="card-body d-flex flex-column">
                        <h6 class="card-title fw-bold mb-3">数据安全治理：从合规到价值创造的转变</h6>
                        <p class="card-text text-muted small flex-grow-1">解析数据安全治理的最新趋势和实践方法...</p>
                        <div class="d-flex justify-content-between align-items-center mt-auto">
                            <small class="text-muted">2024-01-01</small>
                            <a href="#" class="btn btn-outline-primary btn-sm">
                                阅读
                                <svg width="14" height="14" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" class="ms-1">
                                    <path d="M5 12H19M19 12L12 5M19 12L12 19" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                </svg>
                            </a>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-12 col-md-6 col-lg-3">
                <div class="card h-100 border-0 shadow-sm">
                    <img src="https://images.unsplash.com/photo-1504384308090-c894fdcc538d?w=400&h=250&fit=crop&crop=center" class="card-img-top" alt="安全运营">
                    <div class="card-body d-flex flex-column">
                        <h6 class="card-title fw-bold mb-3">SOC建设指南：打造高效安全运营中心</h6>
                        <p class="card-text text-muted small flex-grow-1">分享SOC建设的关键要素和最佳实践经验...</p>
                        <div class="d-flex justify-content-between align-items-center mt-auto">
                            <small class="text-muted">2023-12-28</small>
                            <a href="#" class="btn btn-outline-primary btn-sm">
                                阅读
                                <svg width="14" height="14" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" class="ms-1">
                                    <path d="M5 12H19M19 12L12 5M19 12L12 19" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                </svg>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="text-center mt-5">
            <a href="/news" class="btn btn-primary">
                查看更多文章
                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" class="ms-2">
                    <path d="M5 12H19M19 12L12 5M19 12L12 19" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                </svg>
            </a>
        </div>
    </div>
</section>
{% endblock %}