.new-tickits {
  position: absolute;
  background: #d9534f;
  color: #fff;
  right: -10px;
  top: 0;
  font-size: 10px;
  padding: 3px 8px;
  border-radius: 10px;
  font-weight: 600;
  display: none;
}
.new-tickits.active {
  display: block;
}

#chat_container {
  overflow: visible;
  position: fixed;
  width: 400px;
  height: 540px;
  background: rgb(241, 248, 252);
  right: 10px;
  bottom: 10px;
  z-index: 999999;
  border-radius: 6px;
  box-shadow: 0 8px 10px 1px rgba(0, 0, 0, 0.14),
    0 3px 14px 2px rgba(0, 0, 0, 0.12), 0 5px 5px -3px rgba(0, 0, 0, 0.2);
  opacity: 1;
  overflow: hidden;
  transform: scale3d(1, 1, 1);
  transform-origin: 100% 100%;
  -webkit-transition: transform 0.2s cubic-bezier(0.56, 0.04, 0.36, 1.13),
    opacity 0.2s cubic-bezier(0.55, 0.53, 0.66, 1.1);
  transition: transform 0.2s cubic-bezier(0.56, 0.04, 0.36, 1.13),
    opacity 0.2s cubic-bezier(0.55, 0.53, 0.66, 1.1);
}
#chat_container .content {
  position: relative;
  width: 100%;
  height: 100%;
}

#chat_container .head {
  color: #fff;
  font-size: 18px;
  height: 72px;
  font-weight: 500;
  line-height: 56px;
  padding-left: 20px;
  padding-right: 20px;
  background: url(../../libraries/images/visitor-chat-header.png);
  background-size: 100% 100%;
  line-height: 48px;
}

#chat_container .head .minisize-btn,
#chat_container .head .close-btn {
  float: right;
  text-decoration: none;
  margin-left: 8px;
  color: #fff;
  opacity: 0.7;
  font-weight: 600px;
  margin-top: 16px;
}
#chat_container .head .minisize-btn:hover,
#chat_container .head .close-btn:hover {
  opacity: 1;
}
#chat_container .footer {
  position: absolute;
  width: 100%;
  height: 100px;
  bottom: 0;
}
#chat_container .body {
  /*height: calc(100% - 172px);
	overflow-x: hidden;
	overflow-y: auto;*/
}
.tooltip {
  font-size: 10px;
  color: #888;
  float: right;
  margin-right: 20px;
  line-height: 30px;
}

#chat_container .body .tips {
  width: 100%;
  text-align: center;
  font-size: 12px;
  color: #888;
}
#chat_container .body .message_container {
  max-width: 80%;
  font-weight: 400;
}
#chat_container .body .message-item {
  display: flex;
  padding: 2px 10px;
  margin-top: 6px;
}
#chat_container .body .message-item.left {
  justify-content: flex-start;
}
#chat_container .body .message-item.left .message_data {
  background: #fff;
  padding: 6px 10px;
  border-radius: 6px;
  margin-left: 10px;
  position: relative;
  word-break: break-all;
  font-size: 0.9rem;
}
#chat_container .body .message-item.left .message_date {
  color: #888;
  font-size: 10px;
  text-indent: 12px;
  font-family: verdana, geneva, sans-serif;
}

#chat_container .body .message-item.left .message_data:before {
  content: "";
  display: block;
  width: 0;
  height: 0;
  border-top: 6px solid transparent;
  border-right: 6px solid #fff;
  border-bottom: 6px solid transparent;
  border-left: 6px solid transparent;
  position: absolute;
  top: 7px;
  left: -12px;
}

#chat_container .body .message-item.right {
  justify-content: flex-start;
  flex-flow: row-reverse;
}

#chat_container .body .message-item.right .message_data {
  background: rgb(16, 72, 255);
  color: #fff;
  padding: 6px 10px;
  border-radius: 6px;
  margin-right: 10px;
  position: relative;
  word-break: break-all;
  font-size: 0.9rem;
}

.font-size-75 {
  font-size: 75% !important;
}
.text-white {
  color: #fff !important;
}
.mb-0 {
  margin-bottom: 0;
}
.d-flex {
  display: flex;
}
#chat_container .body .message-item .message_data a {
  color: #004bb0;
  text-decoration: underline;
}
#chat_container .body .message-item .message_data img {
  max-width: 160px;
}
#chat_container .body .message-item .message_data img.mdi-chat-emoji {
  width: 32px;
}
#chat_container .body .message-item .message_data .mdi-chat-attachment {
  background-color: #fff;
  border: 1px solid rgba(0, 0, 0, 0.1);
  border-radius: 0.25rem;
  padding: 0.5rem 0.5rem 0.1rem 0.5rem;
}
#chat_container .body .message-item.right .message_data .mdi-chat-attachment {
  color: #282b31;
}

#chat_container .body .message-item.right .message_date {
  color: #aaa;
  font-weight: 500;
  font-size: 10px;
  margin-right: 12px;
  text-align: right;
}

/*#chat_container .body .message-item.right .message_data:after{
	content:"";
	display: block;
    width: 0;
    height: 0;
    border-top: 6px solid transparent;
    border-right: 6px solid transparent;
    border-bottom: 6px solid transparent;
    border-left: 6px solid #007ffc;
    position: absolute;
    top: 7px;
    right: -12px;
}*/

#chat_container .body .message-item .user_icon {
  height: 28px;
  width: 28px;
  border-radius: 50%;
  color: #fff;
  background: #007ffc;
  font-weight: 500;
  text-align: center;
  line-height: 32px;
  font-size: 18px;
  border: solid 4px #fff;
}
#chat_container .body .message-item img.user_icon {
  background: #fff;
}

#chat_container .footer .text-input {
  height: 70px;
}
#chat_container .footer .text-input [name="message"] {
  height: 68px;
  width: calc(100% - 10px);
  overflow: scroll;
  padding: 2px 8px;
  margin: 0;
  border: none;
  outline: none;
  font-size: 14px;
  background: #fff;
  word-break: break-all;
  overflow-wrap: break-word;
  background-clip: padding-box;
}
#chat_container .footer .text-input [name="message"] img {
  max-height: 60px;
}
#chat_container .wechat img {
  display: none;
}
#chat_container .wechat img.active {
  display: block;
}
#chat_container .footer .text-input [name="message"] .mdi-chat-emoji {
  width: 24px;
}
#chat_container .footer .send {
  height: 30px;
  text-align: left;
  display: block;
  background: #fff;
}
#chat_container .footer .send [name="send_emoji"],
#chat_container .footer .send [name="send_files"],
#chat_container .footer .send [name="send_help"],
#chat_container .footer .send [name="send_edit"] {
  display: inline-block;
  text-decoration: none;
  color: #888;
  font-size: 18px;
  font-weight: 600;
  margin-left: 10px;
  margin-top: 6px;
  vertical-align: -16%;
  position: relative;
}
#chat_container .footer .send [name="send_edit"] {
  display: none;
}
@media screen and (max-width: 576px) {
  #chat_container .footer .send [name="send_edit"] {
    display: inline-block;
  }
}
.emoji_container {
  position: absolute;
  width: 244px;
  height: 200px;
  overflow-wrap: break-word;
  overflow: scroll;
  background: #fff;
  bottom: 24px;
  border-radius: 4px;
  border: solid 1px #ccc;
  box-shadow: 0 8px 10px 1px rgba(0, 0, 0, 0.14),
    0 3px 14px 2px rgba(0, 0, 0, 0.12), 0 5px 5px -3px rgba(0, 0, 0, 0.2);
  text-align: center;
  display: flex;
  flex-wrap: wrap;
}
#chat_container .footer .send [name="send_emoji"].active:after {
  content: "";
  display: block;
  width: 0;
  height: 0;
  border-top: 6px solid #fff;
  border-right: 6px solid transparent;
  border-bottom: 6px solid transparent;
  border-left: 6px solid transparent;
  position: absolute;
  top: -6px;
  left: 6px;
}
.emoji_container .mdi-chat-emoji {
  width: 24px;
  margin: 2px;
}

.emoji_container .dropdown-item {
  display: block;
  width: 30px;
  height: 30px;
  padding: 0;
  clear: both;
  font-weight: 400;
  color: #282b31;
  text-align: inherit;
  white-space: nowrap;
  background-color: transparent;
  border: 0;
  border-radius: 4px;
}
.emoji_container .dropdown-item:hover {
  background: #b9d9ff;
}

.mdi-chat-attachment .mdi-chat-upload-progress {
  position: absolute;
  top: 0px;
  left: 0px;
  right: 0px;
  bottom: 0px;
  z-index: 10;
}
.mdi-chat-attachment .mdi-chat-upload-progress .mdi-chat-upload {
  position: absolute;
  top: 0px;
  bottom: 0px;
  left: 0px;
  width: 0%;
  background-color: rgba(210, 192, 127, 0.3);
}
.uploading-file {
  position: absolute;
  top: 30px;
  line-height: 0;
  left: 25px;
  font-size: 1.25rem !important;
  font-weight: 800;
  animation: spinner-border 1.5s linear infinite;
}
.online-float-image {
  background: #fff;
  z-index: 999999;
  background: #fff;
  box-shadow: 0 8px 10px 1px rgba(0, 0, 0, 0.14),
    0 3px 14px 2px rgba(0, 0, 0, 0.12), 0 5px 5px -3px rgba(0, 0, 0, 0.2);
  left: 50%;
  top: 50%;
  max-height: 80%;
  max-width: 80%;
  position: fixed;
  padding: 20px;
  transform: translate(-50%, -50%);
}
.online-float-image img {
  width: 100%;
}
.online-float-image [name="close"] {
  position: absolute;
  right: 0px;
  top: 0px;
  border: none;
  background: #fff;
  display: block;
  width: 20px;
  height: 20px;
  padding: 0;
}

@keyframes spinner-border {
  to {
    transform: rotate(360deg);
  }
}

.mini-btn {
  color: #fff;
  width: auto;
  font-size: 14px;
  font-weight: bold;
  white-space: nowrap;
  cursor: pointer;
  display: none;
  position: absolute;
  right: 0px;
  bottom: 0px;
  z-index: -1;
  border-radius: 2px;
  overflow: hidden;
  background-color: #1764ff;
  box-shadow: rgba(0, 0, 0, 0.05) 0px 1px 6px, rgba(0, 0, 0, 0.14) 0px 2px 32px;
}

.min-news {
  height: 42px;
  line-height: 42px;
  padding: 0 10px;
  display: flex;
  justify-content: center;
}
.min-news:nth-child(1) {
  padding: 0 24px;
  background-color: rgb(16, 72, 255);
  background: center center no-repeat;
  background-image: url(https://www.mysubmail.com/libraries_v2/images/news.svg);
}
.min-news:nth-child(3) {
  padding: 0 24px;
  background: center center no-repeat;
  background-image: url(https://www.mysubmail.com/libraries_v2/images/top-row.svg);
}
.min-news img {
  width: 24px;
}

#mini_btn.active {
  /* position: fixed; */
  display: block;
  /* z-index: 1; */
}
#chat_container.active {
  height: 0;
  overflow: visible !important;
  bottom: 0;
}
#chat_container .footer.active {
  height: 0;
}
