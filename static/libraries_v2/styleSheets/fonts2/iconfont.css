@font-face {
  font-family: "iconfont"; /* Project id 2425413 */
  src: url('iconfont.woff2') format('woff2'),
       url('iconfont.woff') format('woff'),
       url('iconfont.ttf') format('truetype');
}

.iconfont {
  font-family: "iconfont" !important;
  font-size: 16px;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.iconzuoji-2:before {
  content: "\e61b";
}

.iconqq-3:before {
  content: "\e61c";
}

.iconweixin-3:before {
  content: "\e61d";
}

.iconlogo:before {
  content: "\e61a";
}

.iconjiantou1:before {
  content: "\e619";
}

.iconpinpaixuanchuan:before {
  content: "\e612";
}

.iconmeitixiaoxi:before {
  content: "\e613";
}

.iconduokapianxiaoxi:before {
  content: "\e614";
}

.iconxuanfucaidan:before {
  content: "\e615";
}

.iconwenbenxiaoxi:before {
  content: "\e616";
}

.iconkapianxiaoxi:before {
  content: "\e617";
}

.iconxiaoxihuila:before {
  content: "\e618";
}

.iconyuyin:before {
  content: "\e608";
}

.iconshenfenrenzheng1:before {
  content: "\e609";
}

.iconyijiandenglu_2:before {
  content: "\e60a";
}

.iconzhihuiduanxin:before {
  content: "\e60b";
}

.iconcaixin:before {
  content: "\e60c";
}

.iconduanwangzhi:before {
  content: "\e60d";
}

.iconyoujian:before {
  content: "\e60e";
}

.iconguojiduanxin:before {
  content: "\e60f";
}

.iconduanxin:before {
  content: "\e610";
}

.icon5Gxiaoxi_1:before {
  content: "\e611";
}

.iconMac:before {
  content: "\e606";
}

.iconanzhuo:before {
  content: "\e607";
}

.iconliulan:before {
  content: "\e603";
}

.iconxiazai:before {
  content: "\e604";
}

.iconchakan:before {
  content: "\e605";
}

