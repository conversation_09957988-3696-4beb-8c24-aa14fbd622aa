var WS=function(){function t(t,e=function(t){}){this.url=t,this.websockt=null,this.message=null,this.wsmessage=null,this.locker=!1,this.clentConnectionTimeout=null,this.serverConnectionTimeout=null,this.timeout=3e4,this.cb=e,this.init()}var e=t.prototype;return e.init=function(){this._connection()},e._connection=function(){try{this.websockt=new WebSocket(this.url),this._bind()}catch(t){console.log("Error:"+t),this._connection()}},e._reconnect=function(){var t=this;t.locker||(t.locker=!0,t.clentConnectionTimeout&&clearTimeout(t.clentConnectionTimeout),t.clentConnectionTimeout=setTimeout((function(){t._connection(),t.locker=!1}),t.timeout))},e._bind=function(){var t=this;this.websockt.onclose=function(){console.log("websockt connection closed"),t._reconnect()},this.websockt.onerror=function(){console.log("connection Error"),t._reconnect()},this.websockt.onopen=function(){t._heartbeat()},this.websockt.onmessage=function(e){t._blobReader(e.data),t._heartbeat()}},e._blobReader=function(t){var e=this,i=new FileReader;i.readAsText(t),i.onload=function(){e.onmessage(i.result)},i.onerror=function(t){console.log("Error",t)}},e.onmessage=function(t){this.cb(t);var e=$.Event("ws.onmessage",this);$(this).trigger(e,t)},e._crm_focus=function(t){this.cb(t);var e=$.Event("ws.onmessage",this);$(this).trigger(e,t)},e._crm_unfocus=function(t){this.cb(t);var e=$.Event("ws.onmessage",this);$(this).trigger(e,t)},e._heartbeat=function(){var t=this;t.clentConnectionTimeout&&clearTimeout(t.clentConnectionTimeout),t.serverConnectionTimeout&&clearTimeout(t.serverConnectionTimeout),t.clentConnectionTimeout=setTimeout((function(){t.websockt.send("ping"),t.serverConnectionTimeout=setTimeout((function(){t.websockt.close()}),t.timeout)}),t.timeout)},e.send=function(t){this.websockt.send(t)},e.close=function(){this.websockt.close()},t}();$((function(){var t,e='<div id="chat_container"><div class=\'content\'>\n\t    <div class=\'head\'>\n\t    SUBMAIL 在线咨询\n\t    <a href="javascript:;" class=\'close-btn rrerer\' title=\'关闭\'><svg t="1623321915823" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="2472" width="24" height="24"><path d="M551.424 512l195.072-195.072c9.728-9.728 9.728-25.6 0-36.864l-1.536-1.536c-9.728-9.728-25.6-9.728-35.328 0L514.56 475.136 319.488 280.064c-9.728-9.728-25.6-9.728-35.328 0l-1.536 1.536c-9.728 9.728-9.728 25.6 0 36.864L477.696 512 282.624 707.072c-9.728 9.728-9.728 25.6 0 36.864l1.536 1.536c9.728 9.728 25.6 9.728 35.328 0L514.56 548.864l195.072 195.072c9.728 9.728 25.6 9.728 35.328 0l1.536-1.536c9.728-9.728 9.728-25.6 0-36.864L551.424 512z" fill="#ffffff" p-id="2473"></path></svg></a>\n\n        <p class=\'on-business\' style=\'font-size:12px;line-height:12px;margin:0\'>\n            <svg style=\'vertical-align:-15%;margin-right:2px;\' t="1619505131700" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="2145" width="12" height="12"><path d="M509.579369 908.263046 360.833627 651.900284c0 0-160.984485 64.033385-216.925564 102.586446-55.876612 38.495756-80.520895 155.402349-80.520895 155.402349l894.00532 0c0 0-16.365736-110.143563-82.139765-155.402349C819.320852 715.976648 658.275993 651.900284 658.275993 651.900284L509.579369 908.263046 509.579369 908.263046 509.579369 908.263046 509.579369 908.263046zM509.579369 703.190437l-49.556673 51.296293 24.746614 51.183729-74.369801 102.59361 198.372 0-74.375941-102.59361 24.756847-51.183729L509.579369 703.190437 509.579369 703.190437 509.579369 703.190437 509.579369 703.190437zM514.94661 115.397217c218.262002 0 218.705094 171.21243 218.705094 267.49224 0 96.27981-87.499843 275.15271-218.705094 275.368628C383.682008 658.428977 296.186258 479.111962 296.186258 382.824989 296.186258 286.609647 296.566928 115.397217 514.94661 115.397217L514.94661 115.397217 514.94661 115.397217 514.94661 115.397217zM768.476861 444.301132l-32.747845 0L735.729016 292.026006l68.1174 82.260515L768.476861 444.301132 768.476861 444.301132zM225.663064 374.286522l68.1174-82.260515 0 152.275125-32.793894 0L225.663064 374.286522 225.663064 374.286522zM760.31804 445.356161c0 0-28.506241 152.116513-132.771933 210.391754-50.517557 28.268834 41.300638-62.751183 41.300638-62.751183s55.088666-58.005089 75.095325-147.640572C762.382049 362.161367 760.31804 445.356161 760.31804 445.356161L760.31804 445.356161 760.31804 445.356161zM760.31804 445.356161" p-id="2146" fill="#ffffff"></path></svg>\n            <span class=\'sname\'>等待分配</span>\n            <svg style=\'vertical-align:-15%;margin-right:2px;margin-left:16px;\' t="1619505454004" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="3121" width="12" height="12"><path d="M885.6 230.2L779.1 123.8c-15.3-15.3-35.8-23.8-57.3-23.8-21.7 0-42.1 8.5-57.4 23.8L549.8 238.4c-15.3 15.3-23.8 35.8-23.8 57.3 0 21.7 8.5 42.1 23.8 57.4l83.8 83.8c-19.7 43.4-46.8 82.4-80.5 116.1-33.6 33.8-72.7 60.9-116.1 80.8L353.2 550c-15.3-15.3-35.8-23.8-57.3-23.8-21.7 0-42.1 8.5-57.4 23.8L123.8 664.5c-15.3 15.3-23.8 35.8-23.8 57.4 0 21.7 8.5 42.1 23.8 57.4l106.3 106.3c24.4 24.5 58.1 38.4 92.7 38.4 7.3 0 14.3-0.6 21.2-1.8 134.8-22.2 268.5-93.9 376.4-201.7C828.2 612.8 899.8 479.2 922.3 344c6.8-41.3-6.9-83.8-36.7-113.8z" p-id="3122" fill="#ffffff"></path></svg>\n            <span class=\'smob\'>-</span>\n\n            <a href=\'javascript:;\' class=\'wechat\' style=\'color:#fff;text-decoration: underline;position:relative\'>\n            <svg style=\'vertical-align:-15%;margin-right:2px;margin-left:16px;\' t="1621328071022" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="3335" width="12" height="12"><path d="M683.058 364.695c11 0 22 1.016 32.943 1.976C686.564 230.064 538.896 128 370.681 128c-188.104 0.66-342.237 127.793-342.237 289.226 0 93.068 51.379 169.827 136.725 229.256L130.72 748.43l119.796-59.368c42.918 8.395 77.37 16.79 119.742 16.79 11 0 21.46-0.48 31.914-1.442a259.168 259.168 0 0 1-10.455-71.358c0.485-148.002 128.744-268.297 291.403-268.297l-0.06-0.06z m-184.113-91.992c25.99 0 42.913 16.79 42.913 42.575 0 25.188-16.923 42.579-42.913 42.579-25.45 0-51.38-16.85-51.38-42.58 0-25.784 25.93-42.574 51.38-42.574z m-239.544 85.154c-25.384 0-51.374-16.85-51.374-42.58 0-25.784 25.99-42.574 51.374-42.574 25.45 0 42.918 16.79 42.918 42.575 0 25.188-16.924 42.579-42.918 42.579z m736.155 271.655c0-135.647-136.725-246.527-290.983-246.527-162.655 0-290.918 110.88-290.918 246.527 0 136.128 128.263 246.587 290.918 246.587 33.972 0 68.423-8.395 102.818-16.85l93.809 50.973-25.93-84.677c68.907-51.93 120.286-119.815 120.286-196.033z m-385.275-42.58c-16.923 0-34.452-16.79-34.452-34.179 0-16.79 17.529-34.18 34.452-34.18 25.99 0 42.918 16.85 42.918 34.18 0 17.39-16.928 34.18-42.918 34.18z m188.165 0c-16.984 0-33.972-16.79-33.972-34.179 0-16.79 16.927-34.18 33.972-34.18 25.93 0 42.913 16.85 42.913 34.18 0 17.39-16.983 34.18-42.913 34.18z" fill="#ffffff" p-id="3336"></path></svg>\n            <span>微信联系我</span><image style=\'vertical-align: middle;position: absolute;left: 0;width: 200px;height: 200px;z-index: 999999;top: 19px;background: #fff;padding: 4px;border-radius: 4px;box-shadow: 0 0 8px #999;\' src=""></a>\n        </p>\n\n\n\n        \n\n\t    </div>\n\t    <div style=\'height: calc(100% - 172px);overflow-x: hidden;overflow-y: auto;\' class=\'bd\' id="bd">\n            <div class=\'body writing\' style="position: relative;">\n                <div class="shu-ru" style="opacity: 0;position: absolute;z-index: 1;bottom: -18px;left: 48px;font-size: 12px;color: #888;">对方正在输入...</div>\n            </div>\n        </div>\n\t    <div class=\'footer\'>\n\t    <div class=\'send\'>\n\t\t\t<div class="d-flex justify-content-between align-items-center">\n\t\t\t\t<div>\n\t\t\t\t\t<a href=\'javascript:;\' name=\'send_emoji\'><svg t="1618458008759" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="1191" width="18" height="18"><path d="M512 1024C229.218462 1024 0 794.781538 0 512S229.218462 0 512 0s512 229.218462 512 512-229.218462 512-512 512z m0-157.538462a315.076923 315.076923 0 0 0 315.076923-315.076923H196.923077a315.076923 315.076923 0 0 0 315.076923 315.076923zM354.461538 393.846154a78.769231 78.769231 0 1 0 0-157.538462 78.769231 78.769231 0 0 0 0 157.538462z m315.076924 0a78.769231 78.769231 0 1 0 0-157.538462 78.769231 78.769231 0 0 0 0 157.538462z" fill="#8a8a8a" p-id="1192"></path></svg></a>\n\t\t\t\t\t<a href=\'javascript:;\' name=\'send_files\' class=\'send_files\'><svg t="1618458179671" class="icon" viewBox="0 0 1102 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="3374" width="18" height="18"><path d="M78.769231 0h945.230769a78.769231 78.769231 0 0 1 78.769231 78.769231v78.769231H0V78.769231a78.769231 78.769231 0 0 1 78.769231-78.769231zM0 236.307692v708.923077a78.769231 78.769231 0 0 0 78.769231 78.769231h945.230769a78.769231 78.769231 0 0 0 78.769231-78.769231V236.307692z m787.692308 157.538462H315.076923V315.076923h472.615385z" p-id="3375" fill="#8a8a8a"></path></svg></a>\n\t\t\t\t</div>\n\t\t\t\t<a href=\'javascript:;\' name=\'send_edit\' class=\'send_edit mr-3\'><svg t="1657246128984" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="2278" width="24" height="24"><path d="M865.28 202.5472c-17.1008-15.2576-41.0624-19.6608-62.5664-11.5712L177.7664 427.1104c-23.2448 8.8064-38.5024 29.696-39.6288 54.5792-1.1264 24.8832 11.9808 47.104 34.4064 58.0608l97.5872 47.7184c1.9456 0.9216 3.6864 2.2528 5.2224 3.6864 10.1376 26.112 50.176 128.4096 67.9936 165.376 9.0112 18.8416 25.6 32.0512 40.96 37.7856-1.024-0.1024-2.1504-0.3072-3.3792-0.512 2.9696 1.1264 6.0416 2.048 9.216 2.6624 20.2752 4.096 41.0624-2.1504 55.6032-16.7936l36.352-36.352c6.4512-6.4512 16.5888-7.8848 24.576-3.3792l156.5696 88.8832c9.4208 5.3248 19.8656 8.0896 30.3104 8.0896 8.192 0 16.4864-1.6384 24.2688-5.0176 17.8176-7.68 30.72-22.8352 35.4304-41.6768l130.7648-527.1552c5.632-22.1184-1.6384-45.3632-18.7392-60.5184zM314.2656 578.56l335.0528-191.6928L460.1856 580.608c-3.072 3.1744-5.3248 6.7584-6.8608 10.9568-0.1024 0.2048-0.1024 0.3072-0.2048 0.512-0.4096 1.2288-37.7856 111.5136-59.904 161.3824-4.5056-2.9696-9.9328-7.7824-13.1072-14.4384-16.384-34.4064-54.5792-131.7888-65.8432-160.4608z" p-id="2279" fill="#8a8a8a"></path></svg></a>\n\t\t\t</div>\n\t\t<span class=\'tooltip\'>按住 ALT或option + 上/下 滚动到聊天记录的顶部/底部</span>\n\t    </div>\n\t    <div class=\'text-input\'>\n\t    <div name=\'message\' autofocus="autofocus" contenteditable="true" placeholder="请输入..."></div>\n\t    </div>\n\t    </div>\n\t    </div></div>',i='<div class="message-item right" >\n                <div class="message_data">\n                        <div class="mdi-chat-attachment d-flex position-relative align-items-center">\n                            <div>\n                                <a><span data-dz-name></span></a>\n                                <p class="mb-0 font-size-75"><span data-dz-size></span> </p>\n                            </div>\n                            <div class="mdi-chat-upload-progress">\n                                <div class="mdi-chat-upload" data-dz-uploadprogress></div>\n                            </div>\n                        </div>\n                        <a href="javascript:;" class="text-white font-size-75" data-dz-remove><i class="mdi-icon-sync-crossed2"></i> 取消上传</a>\n                </div>\n                <div class="message_date"></div>\n        </div>';function n(t,e,i=204800){const n=t.target.files[0];if(!n||!/\/(?:jpeg|jpg|png|gif)/i.test(n.type))return;const s=new FileReader;s.onload=function(){const t=this.result;let s=new Image;t.length<=i?o(t,e):(s.onload=function(){o(compress(s,n.type,1600),e),s=null},s.src=t)},s.readAsDataURL(n)}function o(t,e){e&&e(t)}const s=t=>{if(t.clipboardData&&t.clipboardData.items)return new Promise(((e,i)=>{for(let o=0,s=t.clipboardData.items.length;o<s;o++){const s=t.clipboardData.items[o];if("string"===s.kind)2==t.clipboardData.items.length&&"text/plain"===t.clipboardData.items[1].type?t.clipboardData.items[1].getAsString((t=>{e(t)})):s.getAsString((t=>{e(t)}));else if("file"===s.kind){n({target:{files:[s.getAsFile()]}},(t=>{e(t)}))}else i(new Error("Not allow to paste this type!"))}}))};let a,c,r,l=Math.random().toString().split(".");l=l[1];let m=document.title;var d=function(t){if("granted"==Notification.permission){var e=new Notification("您有新的消息！",{body:t});e.onclick=function(){0==$("#chat_container").length&&$("#webchat").click(),e.close()}}};let u={page:1,state:!0,holdon:!1,getCookie:function(){if(t)$.get("/welcome/getClientId_v2",{cli:a},(function(t){t=$.parseJSON(t);$("#chat_container .sname").text(t.sname);const e=/Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent),i=t.smob;if(i&&""!==i.trim())if(e){const t=`<a style='color:#fff' href="tel:${i.replace(/\D/g,"")}" class="smob">${i}</a>`;$("#chat_container .smob").html(t)}else $("#chat_container .smob").text(i);else $("#chat_container .smob").text("暂无联系方式");t.sqrcode&&null!=t.sqrcode?$("#chat_container .wechat img").attr("src",t.sqrcode):$("#chat_container .wechat").remove()})),u.init();else{let t=$.cookie("onlinechat_id");void 0===t&&(t="none"),$.get("/welcome/getClientId",{cli:t},(function(t){t=$.parseJSON(t);a=t.client_id,c=t.timestamp,r=t.token,$("#chat_container .sname").text(t.sname);const e=/Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent),i=t.smob;if(i&&""!==i.trim())if(e){const t=`<a style='color:#fff' href="tel:${i.replace(/\D/g,"")}" class="smob">${i}</a>`;$("#chat_container .smob").html(t)}else $("#chat_container .smob").text(i);else $("#chat_container .smob").text("-");t.sqrcode&&null!=t.sqrcode?$("#chat_container .wechat img").attr("src",t.sqrcode):$("#chat_container .wechat").remove(),$.cookie("onlinechat_id",t.client_id,{expires:365,path:"/"}),u.init()}))}},flashState:!0,startFlashTitle:function(t){window.Notification?"granted"==Notification.permission?d(t):"denied"!=Notification.permission?Notification.requestPermission((function(t){d()})):document.title="您有新的消息":document.title="您有新的消息"},sendmsg:function(t){let e=new Date,i=e.getHours();i=i>=10?i:"0"+i;let n=e.getMinutes();n=n>=10?n:"0"+n;let o=i+":"+n;this.dialog(t,0,o),this.submit(t)},dialog:function(t,e,i){let n="",o="";"0"==e?(n='<div class="message-item @var(pos)">\n\t\t\t\t<div class="message_container">\n\t\t\t\t<div class="message_data">@var(message)</div>\n\t\t\t\t<div class="message_date">@var(timer)</div>\n\t\t\t\t</div>\n\t\t\t\t</div>',o="right"):(n='<div class="message-item @var(pos)">\n\t\t\t\t<img src="/libraries/images/submail.logo.icon.svg" class="user_icon">\n\t\t\t\t<div class="message_container">\n\t\t\t\t<div class="message_data">@var(message)</div>\n\t\t\t\t<div class="message_date">@var(timer)</div>\n\t\t\t\t</div>\n\t\t\t\t</div>\n               ',o="left"),n=n.replace(/@var\(pos\)/g,o),n=n.replace(/@var\(message\)/g,t),n=n.replace(/@var\(timer\)/g,i),$("#chat_container .content .body").append(n),$("#chat_container .content .bd").scrollTop($("#chat_container .content .body").height())},dialogs:function(t){let e="",i=$("#chat_container .content .body").height();$.each(t,(function(t,i){let n="",o="";"0"==i.type?(n='<div class="message-item @var(pos)">\n\t\t\t\t\t<div class="message_container">\n\t\t\t\t\t<div class="message_data">@var(message)</div>\n\t\t\t\t\t<div class="message_date">@var(timer)</div>\n\t\t\t\t\t</div>\n\t\t\t\t\t</div>',o="right"):(n='<div class="message-item @var(pos)">\n\t\t\t\t\t<img src="/libraries/images/submail.logo.icon.svg" class="user_icon">\n\t\t\t\t\t<div class="message_container">\n\t\t\t\t\t<div class="message_data">@var(message)</div>\n\t\t\t\t\t<div class="message_date">@var(timer)</div>\n\t\t\t\t\t</div>\n\t\t\t\t\t</div>',o="left"),n=n.replace(/@var\(pos\)/g,o),n=n.replace(/@var\(message\)/g,i.content),n=n.replace(/@var\(timer\)/g,i.datetime),e=n+e})),$("#chat_container .content .body").prepend(e),setTimeout((function(){let t=$("#chat_container .content .body").height();$("#chat_container .content .bd").scrollTop(t-i)}),300)},submit:function(t,e=""){let i={};i.tim=Math.random(),i.message=t,i.session_id=l,i.client_id=a,$.post("/welcome/onlineChatSubmitPost",i,(function(t){"success"!=(t=$.parseJSON(t)).status&&alert("服务器异常")}))},init:function(){if(t)document.querySelector("#chat_container [name=message]").addEventListener("input",u.focusAddEventliste),document.querySelector("#chat_container [name=message]").addEventListener("blur",u.blurAddEventliste);else{let e;t=new WS("wss://websocket-im.mysubmail.com/onlinechat/"+a+"/"+c+"/"+r),console.log("web socket 已开启"+a+c+r),document.querySelector("#chat_container [name=message]").addEventListener("input",u.focusAddEventliste),document.querySelector("#chat_container [name=message]").addEventListener("blur",u.blurAddEventliste),$(t).on("ws.onmessage",(function(t,n){if("_crm_focus"==n){const t=document.getElementById("bd");t.scrollTop=t.scrollHeight,$(".shu-ru").attr("style","opacity:1;position: absolute;z-index: 1;bottom: -18px;left: 48px;font-size: 12px;color: #888;"),clearTimeout(e),e=setTimeout((()=>{$(".shu-ru").attr("style","opacity:0;position: absolute;z-index: 1;bottom: -18px;left: 48px;font-size: 12px;color: #888;")}),1e3)}else"_crm_unfocus"==n&&$(".shu-ru").attr("style","opacity:0;position: absolute;z-index: 1;bottom: -18px;left: 48px;font-size: 12px;color: #888;");try{"tickt"==(n=$.parseJSON(n)).type&&n.message.sessionid!=l&&(u.holdon=!1,u.dialog(n.message.message,n.message.type,n.message.create_at),$(".new-tickits").removeClass("active"),window.Notification&&"default"==Notification.permission&&Notification.requestPermission(),$("#chat_container [name=message]").focus(),document.querySelector("#chat_container [name=message]").addEventListener("paste",(async t=>{if("file"==t.clipboardData.items[0].kind){t.preventDefault();const e=await s(t);/^data:image\/png;base64,/.test(e)?document.execCommand("insertImage",!1,e):document.execCommand("insertText",!1,e)}})),document.querySelector("#chat_container [name=message]").ondragstart=function(){return!1},$("#chat_container .content .body").dropzone({url:"/welcome/upload",previewTemplate:i,autoQueue:!0,timeout:6e4,maxFilesize:10,previewsContainer:"#chat_container .content .body",clickable:"[name=send_files]",success:function(t,e){if("success"==(e=$.parseJSON(e)).status){t.previewElement.remove();let i=new Date,n=i.getHours();n=n>=10?n:"0"+n;let o=i.getMinutes();o=o>=10?o:"0"+o;let s=n+":"+o;u.dialog(e.message,0,s)}else alert(e.message),t.previewElement.remove()},addedfiles:function(t){},sending:function(t,e,i){i.append("session_id",l),i.append("client_id",a)},drop:function(t,e){},maxfilesexceeded:function(){},error:function(t,e){alert(e),t.previewElement.remove()}}),document.querySelector("#chat_container .content .bd").onscroll=function(){0==$(this).scrollTop()&&u.state&&u.loadmore()},u.getCookie())}catch(t){console.log(t)}}))}var e={};e.client_id=a,e.status="live",$.post("/welcome/sendStatus",e),$.get("/welcome/getClientLogs",{ws_client_id:a,page:u.page},(function(t){let e=$.parseJSON(t);e.logs.length>0&&(u.page++,u.dialogs(e.logs),setTimeout((function(){$("#chat_container .content .bd").scrollTop($("#chat_container .content .body").height())}),200)),setTimeout((function(){let t="请稍等，您的商务经理可能正在为您查询资料，您也可以通过其他方式联系您的商务经理，联系人：<b>"+$("#chat_container .sname").text()+"</b>、联系电话：<b>"+$("#chat_container .smob").text()+'</b>、微信二维码：</br><image src="'+$("#chat_container .wechat img").attr("src")+'">',e=new Date,i=e.getHours();i=i>=10?i:"0"+i;let n=e.getMinutes();n=n>=10?n:"0"+n;let o=i+":"+n;u.dialog(t,1,o),$.get("/welcome/getAutoQuestions",{},(function(t){let e=$.parseJSON(t);if(e.auto_questions.length>0){u.state=!1;let t="欢迎访问赛邮云通信官网，点击查看常见问题！";t+="<ul style='font-size:12px;margin-top:6px;'>",$.each(e.auto_questions,(function(e,i){t+="<li style='list-style:inside'><a href='javascript:;' class='auto_question' auto_title='"+i.content+"' id='"+i.id+"'>"+i.title+"</a></li>"})),t+="</ul>";let i="您好，这里是人工客服，请问有什么可以帮到您呢？",n=1,o=new Date,s=o.getHours();s=s>=10?s:"0"+s;let a=o.getMinutes();a=a>=10?a:"0"+a;let c=s+":"+a;u.dialog(t,n,c),u.dialog(i,n,c)}}))}),1e3)}))},loadmore:function(){$.get("/welcome/getClientLogs",{ws_client_id:a,page:u.page},(function(t){let e=$.parseJSON(t);e.logs.length<50?u.state=!1:u.page++,u.dialogs(e)}))},getNewTickit:function(){var t="hidden"in document?"hidden":"webkitHidden"in document?"webkitHidden":"mozHidden"in document?"mozHidden":null;a?($("#webchat .badge").css("display","none"),$.get("/welcome/getNewTickit",{ws_client_id:a},(function(e){"success"==(e=$.parseJSON(e)).status&&(document[t]?(u.startFlashTitle(e.lastmsg),u.flashState=!0,$(".new-tickits").addClass("active")):0==$("#chat_container").length&&$("#webchat").click())}))):setTimeout((function(){$("#webchat .badge").css("display","inline-block"),$("#webchat").addClass("shake")}),3e3),setTimeout((function(){u.getNewTickit()}),5e3)},focusAddEventliste:function(){t.websockt.send("_usr_focus")},blurAddEventliste:function(){t.websockt.send("_usr_unfocus")}};u.getNewTickit(),window.addEventListener("beforeunload",(function(t){var e={};e.client_id=a,e.status="afk",$.post("/welcome/sendStatus",e),document.querySelector("#chat_container [name=message]").removeEventListener("input",u.focusAddEventliste),document.querySelector("#chat_container [name=message]").removeEventListener("blur",u.blurAddEventliste)})),$(document).on("click","#chat_container .auto_question",(function(){let t=$(this).text(),e=$(this).attr("auto_title"),i=new Date,n=i.getHours();n=n>=10?n:"0"+n;let o=i.getMinutes();o=o>=10?o:"0"+o;let s=n+":"+o;u.dialog(t,0,s),setTimeout((function(){u.dialog(e,1,s)}),500);let a=$(this).attr("id");$.post("/welcome/aqhit",{id:a})})),$(document).on("click",".emoji_container .dropdown-item",(function(){document.execCommand("insertHTML",!1,$(this).html()),$(".emoji_container").remove()})),$(document).on("click","#chat_container .close-btn",(function(){var t=document.getElementsByClassName("disa");$(t).removeAttr("disabled").css("pointer-events","auto"),$("#chat_container").remove(),u.page=1,u.state=!0})),$(document).on("click","#chat_container [name=send_emoji]",(function(){return 0===$(".emoji_container").length?($(this).addClass("active"),
$(this).append('\n\t  <div class=\'emoji_container\'>\n\t  <button class="dropdown-item"><img src=\'/libraries/emoji/0.png\' class="mdi-chat-emoji"></button>\n\t  <button class="dropdown-item"><img src=\'/libraries/emoji/1.png\' class="mdi-chat-emoji"></button>\n\t  <button class="dropdown-item"><img src=\'/libraries/emoji/2.png\' class="mdi-chat-emoji"></button>\n\t  <button class="dropdown-item"><img src=\'/libraries/emoji/3.png\' class="mdi-chat-emoji"></button>\n\t  <button class="dropdown-item"><img src=\'/libraries/emoji/4.png\' class="mdi-chat-emoji"></button>\n\t  <button class="dropdown-item"><img src=\'/libraries/emoji/5.png\' class="mdi-chat-emoji"></button>\n\t  <button class="dropdown-item"><img src=\'/libraries/emoji/6.png\' class="mdi-chat-emoji"></button>\n\t  <button class="dropdown-item"><img src=\'/libraries/emoji/8.png\' class="mdi-chat-emoji"></button>\n\t  <button class="dropdown-item"><img src=\'/libraries/emoji/9.png\' class="mdi-chat-emoji"></button>\n\t  <button class="dropdown-item"><img src=\'/libraries/emoji/10.png\' class="mdi-chat-emoji"></button>\n\t  <button class="dropdown-item"><img src=\'/libraries/emoji/11.png\' class="mdi-chat-emoji"></button>\n\t  <button class="dropdown-item"><img src=\'/libraries/emoji/12.png\' class="mdi-chat-emoji"></button>\n\t  <button class="dropdown-item"><img src=\'/libraries/emoji/13.png\' class="mdi-chat-emoji"></button>\n\t  <button class="dropdown-item"><img src=\'/libraries/emoji/14.png\' class="mdi-chat-emoji"></button>\n\t  <button class="dropdown-item"><img src=\'/libraries/emoji/15.png\' class="mdi-chat-emoji"></button>\n\t  <button class="dropdown-item"><img src=\'/libraries/emoji/16.png\' class="mdi-chat-emoji"></button>\n\t  <button class="dropdown-item"><img src=\'/libraries/emoji/17.png\' class="mdi-chat-emoji"></button>\n\t  <button class="dropdown-item"><img src=\'/libraries/emoji/18.png\' class="mdi-chat-emoji"></button>\n\t  <button class="dropdown-item"><img src=\'/libraries/emoji/19.png\' class="mdi-chat-emoji"></button>\n\t  <button class="dropdown-item"><img src=\'/libraries/emoji/20.png\' class="mdi-chat-emoji"></button>\n\t  <button class="dropdown-item"><img src=\'/libraries/emoji/23.png\' class="mdi-chat-emoji"></button>\n\t  <button class="dropdown-item"><img src=\'/libraries/emoji/24.png\' class="mdi-chat-emoji"></button>\n\t  <button class="dropdown-item"><img src=\'/libraries/emoji/25.png\' class="mdi-chat-emoji"></button>\n\t  <button class="dropdown-item"><img src=\'/libraries/emoji/26.png\' class="mdi-chat-emoji"></button>\n\t  <button class="dropdown-item"><img src=\'/libraries/emoji/27.png\' class="mdi-chat-emoji"></button>\n\t  <button class="dropdown-item"><img src=\'/libraries/emoji/28.png\' class="mdi-chat-emoji"></button>\n\t  <button class="dropdown-item"><img src=\'/libraries/emoji/29.png\' class="mdi-chat-emoji"></button>\n\t  <button class="dropdown-item"><img src=\'/libraries/emoji/30.png\' class="mdi-chat-emoji"></button>\n\t  <button class="dropdown-item"><img src=\'/libraries/emoji/31.png\' class="mdi-chat-emoji"></button>\n\t  <button class="dropdown-item"><img src=\'/libraries/emoji/32.png\' class="mdi-chat-emoji"></button>\n\t  <button class="dropdown-item"><img src=\'/libraries/emoji/33.png\' class="mdi-chat-emoji"></button>\n\t  <button class="dropdown-item"><img src=\'/libraries/emoji/34.png\' class="mdi-chat-emoji"></button>\n\t  <button class="dropdown-item"><img src=\'/libraries/emoji/35.png\' class="mdi-chat-emoji"></button>\n\t  <button class="dropdown-item"><img src=\'/libraries/emoji/36.png\' class="mdi-chat-emoji"></button>\n\t  <button class="dropdown-item"><img src=\'/libraries/emoji/37.png\' class="mdi-chat-emoji"></button>\n\t  <button class="dropdown-item"><img src=\'/libraries/emoji/38.png\' class="mdi-chat-emoji"></button>\n\t  <button class="dropdown-item"><img src=\'/libraries/emoji/39.png\' class="mdi-chat-emoji"></button>\n\t  <button class="dropdown-item"><img src=\'/libraries/emoji/40.png\' class="mdi-chat-emoji"></button>\n\t  <button class="dropdown-item"><img src=\'/libraries/emoji/41.png\' class="mdi-chat-emoji"></button>\n\t  <button class="dropdown-item"><img src=\'/libraries/emoji/42.png\' class="mdi-chat-emoji"></button>\n\t  <button class="dropdown-item"><img src=\'/libraries/emoji/43.png\' class="mdi-chat-emoji"></button>\n\t  <button class="dropdown-item"><img src=\'/libraries/emoji/44.png\' class="mdi-chat-emoji"></button>\n\t  <button class="dropdown-item"><img src=\'/libraries/emoji/45.png\' class="mdi-chat-emoji"></button>\n\t  <button class="dropdown-item"><img src=\'/libraries/emoji/46.png\' class="mdi-chat-emoji"></button>\n\t  <button class="dropdown-item"><img src=\'/libraries/emoji/47.png\' class="mdi-chat-emoji"></button>\n\t  <button class="dropdown-item"><img src=\'/libraries/emoji/48.png\' class="mdi-chat-emoji"></button>\n\t  <button class="dropdown-item"><img src=\'/libraries/emoji/49.png\' class="mdi-chat-emoji"></button>\n\t  <button class="dropdown-item"><img src=\'/libraries/emoji/50.png\' class="mdi-chat-emoji"></button>\n\t  <button class="dropdown-item"><img src=\'/libraries/emoji/51.png\' class="mdi-chat-emoji"></button>\n\t  <button class="dropdown-item"><img src=\'/libraries/emoji/52.png\' class="mdi-chat-emoji"></button>\n\t  <button class="dropdown-item"><img src=\'/libraries/emoji/53.png\' class="mdi-chat-emoji"></button>\n\t  <button class="dropdown-item"><img src=\'/libraries/emoji/54.png\' class="mdi-chat-emoji"></button>\n\t  <button class="dropdown-item"><img src=\'/libraries/emoji/55.png\' class="mdi-chat-emoji"></button>\n\t  <button class="dropdown-item"><img src=\'/libraries/emoji/56.png\' class="mdi-chat-emoji"></button>\n\t  <button class="dropdown-item"><img src=\'/libraries/emoji/57.png\' class="mdi-chat-emoji"></button>\n\t  <button class="dropdown-item"><img src=\'/libraries/emoji/58.png\' class="mdi-chat-emoji"></button>\n\t  <button class="dropdown-item"><img src=\'/libraries/emoji/59.png\' class="mdi-chat-emoji"></button>\n\t  <button class="dropdown-item"><img src=\'/libraries/emoji/60.png\' class="mdi-chat-emoji"></button>\n\t  <button class="dropdown-item"><img src=\'/libraries/emoji/61.png\' class="mdi-chat-emoji"></button>\n\t  <button class="dropdown-item"><img src=\'/libraries/emoji/62.png\' class="mdi-chat-emoji"></button>\n\t  <button class="dropdown-item"><img src=\'/libraries/emoji/63.png\' class="mdi-chat-emoji"></button>\n\t  <button class="dropdown-item"><img src=\'/libraries/emoji/64.png\' class="mdi-chat-emoji"></button>\n\t  <button class="dropdown-item"><img src=\'/libraries/emoji/65.png\' class="mdi-chat-emoji"></button>\n\t  <button class="dropdown-item"><img src=\'/libraries/emoji/66.png\' class="mdi-chat-emoji"></button>\n\t  <button class="dropdown-item"><img src=\'/libraries/emoji/67.png\' class="mdi-chat-emoji"></button>\n\t  <button class="dropdown-item"><img src=\'/libraries/emoji/68.png\' class="mdi-chat-emoji"></button>\n\t  <button class="dropdown-item"><img src=\'/libraries/emoji/69.png\' class="mdi-chat-emoji"></button>\n\t  <button class="dropdown-item"><img src=\'/libraries/emoji/70.png\' class="mdi-chat-emoji"></button>\n\t  <button class="dropdown-item"><img src=\'/libraries/emoji/71.png\' class="mdi-chat-emoji"></button>\n\t  <button class="dropdown-item"><img src=\'/libraries/emoji/72.png\' class="mdi-chat-emoji"></button>\n\t  <button class="dropdown-item"><img src=\'/libraries/emoji/73.png\' class="mdi-chat-emoji"></button>\n\t  <button class="dropdown-item"><img src=\'/libraries/emoji/74.png\' class="mdi-chat-emoji"></button>\n\t  <button class="dropdown-item"><img src=\'/libraries/emoji/75.png\' class="mdi-chat-emoji"></button>\n\t  <button class="dropdown-item"><img src=\'/libraries/emoji/76.png\' class="mdi-chat-emoji"></button>\n\t  <button class="dropdown-item"><img src=\'/libraries/emoji/77.png\' class="mdi-chat-emoji"></button>\n\t  <button class="dropdown-item"><img src=\'/libraries/emoji/78.png\' class="mdi-chat-emoji"></button>\n\t  <button class="dropdown-item"><img src=\'/libraries/emoji/79.png\' class="mdi-chat-emoji"></button>\n\t  <button class="dropdown-item"><img src=\'/libraries/emoji/80.png\' class="mdi-chat-emoji"></button>\n\t  <button class="dropdown-item"><img src=\'/libraries/emoji/81.png\' class="mdi-chat-emoji"></button>\n\t  <button class="dropdown-item"><img src=\'/libraries/emoji/82.png\' class="mdi-chat-emoji"></button>\n\t  <button class="dropdown-item"><img src=\'/libraries/emoji/83.png\' class="mdi-chat-emoji"></button>\n\t  <button class="dropdown-item"><img src=\'/libraries/emoji/84.png\' class="mdi-chat-emoji"></button>\n\t  <button class="dropdown-item"><img src=\'/libraries/emoji/85.png\' class="mdi-chat-emoji"></button>\n\t  <button class="dropdown-item"><img src=\'/libraries/emoji/86.png\' class="mdi-chat-emoji"></button>\n\t  <button class="dropdown-item"><img src=\'/libraries/emoji/87.png\' class="mdi-chat-emoji"></button>\n\t  <button class="dropdown-item"><img src=\'/libraries/emoji/88.png\' class="mdi-chat-emoji"></button>\n\t  <button class="dropdown-item"><img src=\'/libraries/emoji/89.png\' class="mdi-chat-emoji"></button>\n\t  <button class="dropdown-item"><img src=\'/libraries/emoji/90.png\' class="mdi-chat-emoji"></button>\n\t  <button class="dropdown-item"><img src=\'/libraries/emoji/91.png\' class="mdi-chat-emoji"></button>\n\t  <button class="dropdown-item"><img src=\'/libraries/emoji/92.png\' class="mdi-chat-emoji"></button>\n\t  <button class="dropdown-item"><img src=\'/libraries/emoji/93.png\' class="mdi-chat-emoji"></button>\n\t  <button class="dropdown-item"><img src=\'/libraries/emoji/94.png\' class="mdi-chat-emoji"></button>\n\t  <button class="dropdown-item"><img src=\'/libraries/emoji/95.png\' class="mdi-chat-emoji"></button>\n\t  <button class="dropdown-item"><img src=\'/libraries/emoji/96.png\' class="mdi-chat-emoji"></button>\n\t  <button class="dropdown-item"><img src=\'/libraries/emoji/97.png\' class="mdi-chat-emoji"></button>\n\t  <button class="dropdown-item"><img src=\'/libraries/emoji/98.png\' class="mdi-chat-emoji"></button>\n\t  </div>')):($(this).removeClass("active"),$(".emoji_container").remove()),!1})),$(document).on("click","#chat_container .emoji_container",(function(){return!1})),$(document).on("mouseover","#chat_container .wechat",(function(){$("#chat_container .wechat img").addClass("active")})),$(document).on("mouseout","#chat_container .wechat",(function(){$("#chat_container .wechat img").removeClass("active")})),$(document).on("click",(function(t){if(document.title=m,t=t||window.event,$("#chat_container").length>0&&"input"!=t.target.localName&&"textarea"!=t.target.localName&&!t.target.isContentEditable){$("#chat_container [name=send_emoji]").removeClass("active"),$("#chat_container .emoji_container").remove();let t=document.querySelector("#chat_container [name=message]");if(window.getSelection){if(_selobj=window.getSelection(),_selobj.isCollapsed){t.focus();var e=window.getSelection();e.selectAllChildren(t),e.collapseToEnd()}}else document.selection}})),$(document).on("click","#mini_btn",(function(){$(".new-tickits").removeClass("active"),window.Notification&&"default"==Notification.permission&&Notification.requestPermission(),$("body").append(e),$("#chat_container [name=message]").focus(),document.querySelector("#chat_container [name=message]").addEventListener("paste",(async t=>{if("file"==t.clipboardData.items[0].kind){t.preventDefault();const e=await s(t);/^data:image\/png;base64,/.test(e)?document.execCommand("insertImage",!1,e):document.execCommand("insertText",!1,e)}})),document.querySelector("#chat_container [name=message]").ondragstart=function(){return!1},$("#chat_container .content .body").dropzone({url:"/welcome/upload",previewTemplate:i,autoQueue:!0,timeout:6e4,maxFilesize:10,previewsContainer:"#chat_container .content .body",clickable:"[name=send_files]",success:function(t,e){if("success"==(e=$.parseJSON(e)).status){t.previewElement.remove();let i=new Date,n=i.getHours();n=n>=10?n:"0"+n;let o=i.getMinutes();o=o>=10?o:"0"+o;let s=n+":"+o;u.dialog(e.message,0,s)}else alert(e.message),t.previewElement.remove()},addedfiles:function(t){},sending:function(t,e,i){i.append("session_id",l),i.append("client_id",a)},drop:function(t,e){},maxfilesexceeded:function(){},error:function(t,e){alert(e),t.previewElement.remove()}}),document.querySelector("#chat_container .content .bd").onscroll=function(){0==$(this).scrollTop()&&u.state&&u.loadmore()},u.getCookie()})),$(document).on("click","#webchat",(function(){$(".new-tickits").removeClass("active"),window.Notification&&"default"==Notification.permission&&Notification.requestPermission(),$("body").append(e),$("#chat_container [name=message]").focus(),document.querySelector("#chat_container [name=message]").addEventListener("paste",(async t=>{if("file"==t.clipboardData.items[0].kind){t.preventDefault();const e=await s(t);/^data:image\/png;base64,/.test(e)?document.execCommand("insertImage",!1,e):document.execCommand("insertText",!1,e)}})),document.querySelector("#chat_container [name=message]").ondragstart=function(){return!1},$("#chat_container .content .body").dropzone({url:"/welcome/upload",previewTemplate:i,autoQueue:!0,timeout:6e4,maxFilesize:10,previewsContainer:"#chat_container .content .body",clickable:"[name=send_files]",success:function(t,e){if("success"==(e=$.parseJSON(e)).status){t.previewElement.remove();let i=new Date,n=i.getHours();n=n>=10?n:"0"+n;let o=i.getMinutes();o=o>=10?o:"0"+o;let s=n+":"+o;u.dialog(e.message,0,s)}else alert(e.message),t.previewElement.remove()},addedfiles:function(t){},sending:function(t,e,i){i.append("session_id",l),i.append("client_id",a)},drop:function(t,e){},maxfilesexceeded:function(){},error:function(t,e){alert(e),t.previewElement.remove()}}),document.querySelector("#chat_container .content .bd").onscroll=function(){0==$(this).scrollTop()&&u.state&&u.loadmore()},u.getCookie(1)})),$(document).on("click",".send_edit",(function(){let e=$("#chat_container").find("div[name=message]").html(),n=e.replace(new RegExp("&nbsp;","gm"),"").trim();for(;0===e.indexOf("<div><br></div>");)e=e.replace("<div><br></div>","");if(""!=n){if(t)u.sendmsg(e),u.holdon=!0,setTimeout((function(){if(u.holdon){let t="请稍等，您的商务经理可能正在为您查询资料，您也可以通过其他方式联系您的商务经理，联系人：<b>"+$("#chat_container .sname").text()+"</b>、联系电话：<b>"+$("#chat_container .smob").text()+'</b>、微信二维码：</br><image src="'+$("#chat_container .wechat img").attr("src")+'">',e=1,i=new Date,n=i.getHours();n=n>=10?n:"0"+n;let o=i.getMinutes();o=o>=10?o:"0"+o;let s=n+":"+o;u.dialog(t,e,s),u.holdon=!1}}),15e3);else{let n=$.cookie("onlinechat_id");void 0===n&&(n="none"),$.get("/welcome/getClientId",{cli:n},(function(n){n=$.parseJSON(n);a=n.client_id,c=n.timestamp,r=n.token,$.cookie("onlinechat_id",n.client_id,{expires:365,path:"/"}),u.holdon=!0,setTimeout((function(){if(u.holdon){let t="请稍等，您的商务经理可能正在为您查询资料，您也可以通过其他方式联系您的商务经理，联系人：<b>"+$("#chat_container .sname").text()+"</b>、联系电话：<b>"+$("#chat_container .smob").text()+'</b>、微信二维码：</br><image src="'+$("#chat_container .wechat img").attr("src")+'">',e=1,i=new Date,n=i.getHours();n=n>=10?n:"0"+n;let o=i.getMinutes();o=o>=10?o:"0"+o;let s=n+":"+o;u.dialog(t,e,s),u.holdon=!1}}),15e3),t=new WS("wss://websocket-im.mysubmail.com/onlinechat/"+a+"/"+c+"/"+r),console.log("web socket 已开启"+a+c+r),$(t).on("ws.onmessage",(function(t,e){try{"tickt"==(e=$.parseJSON(e)).type&&e.message.sessionid!=l&&(u.holdon=!1,u.dialog(e.message.message,e.message.type,e.message.create_at),$(".new-tickits").removeClass("active"),window.Notification&&"default"==Notification.permission&&Notification.requestPermission(),$("#chat_container [name=message]").focus(),document.querySelector("#chat_container [name=message]").addEventListener("paste",(async t=>{if("file"==t.clipboardData.items[0].kind){t.preventDefault();const e=await s(t);/^data:image\/png;base64,/.test(e)?document.execCommand("insertImage",!1,e):document.execCommand("insertText",!1,e)}})),document.querySelector("#chat_container [name=message]").ondragstart=function(){return!1},$("#chat_container .content .body").dropzone({url:"/welcome/upload",previewTemplate:i,autoQueue:!0,timeout:6e4,maxFilesize:10,previewsContainer:"#chat_container .content .body",clickable:"[name=send_files]",success:function(t,e){if("success"==(e=$.parseJSON(e)).status){t.previewElement.remove();let i=new Date,n=i.getHours();n=n>=10?n:"0"+n;let o=i.getMinutes();o=o>=10?o:"0"+o;let s=n+":"+o;u.dialog(e.message,0,s)}else alert(e.message),t.previewElement.remove()},addedfiles:function(t){},sending:function(t,e,i){i.append("session_id",l),i.append("client_id",a)},drop:function(t,e){},maxfilesexceeded:function(){},error:function(t,e){alert(e),t.previewElement.remove()}}),document.querySelector("#chat_container .content .bd").onscroll=function(){0==$(this).scrollTop()&&u.state&&u.loadmore()},u.getCookie())}catch(t){console.log(t)}})),u.sendmsg(e)}))}$("#chat_container").find("div[name=message]").html("")}})),$(document).on("keyup","#chat_container [name=message]",(function(t){return!1})),$(document).on("keydown","#chat_container [name=message]",(function(e){let n=e.keyCode?e.keyCode:e.which,o=$(this);if(13===n&&e.shiftKey)o.next().focus();else if(13===n&&e.ctrlKey)o.next().focus();else if(13===n&&e.altKey)o.next().focus();else if(13===n&&e.metaKey)o.next().focus();else if(13!==n||e.shiftKey||e.ctrlKey||e.altKey||e.metaKey)38==n&&e.altKey?$("#chat_container .content .bd").scrollTop(0):40==n&&e.altKey?$("#chat_container .content .bd").scrollTop($("#chat_container .content .body").height()):13===n&&e.preventDefault();else{e.preventDefault();let n=$(this).html(),o=n.replace(new RegExp("&nbsp;","gm"),"").trim();for(;0===n.indexOf("<div><br></div>");)n=n.replace("<div><br></div>","");if(""!=o){if(t)u.sendmsg(n),u.holdon=!0,setTimeout((function(){if(u.holdon){let t="请稍等，您的商务经理可能正在为您查询资料，您也可以通过其他方式联系您的商务经理，联系人：<b>"+$("#chat_container .sname").text()+"</b>、联系电话：<b>"+$("#chat_container .smob").text()+'</b>、微信二维码：</br><image src="'+$("#chat_container .wechat img").attr("src")+'">',e=1,i=new Date,n=i.getHours();n=n>=10?n:"0"+n;let o=i.getMinutes();o=o>=10?o:"0"+o;let s=n+":"+o;u.dialog(t,e,s),u.holdon=!1}}),15e3);else{let e=$.cookie("onlinechat_id");void 0===e&&(e="none"),$.get("/welcome/getClientId",{cli:e},(function(e){e=$.parseJSON(e);let o;a=e.client_id,c=e.timestamp,r=e.token,$.cookie("onlinechat_id",e.client_id,{expires:365,path:"/"}),u.holdon=!0,setTimeout((function(){if(u.holdon){let t="请稍等，您的商务经理可能正在为您查询资料，您也可以通过其他方式联系您的商务经理，联系人：<b>"+$("#chat_container .sname").text()+"</b>、联系电话：<b>"+$("#chat_container .smob").text()+'</b>、微信二维码：</br><image src="'+$("#chat_container .wechat img").attr("src")+'">',e=1,i=new Date,n=i.getHours();n=n>=10?n:"0"+n;let o=i.getMinutes();o=o>=10?o:"0"+o;let s=n+":"+o;u.dialog(t,e,s),u.holdon=!1}}),15e3),t=new WS("wss://websocket-im.mysubmail.com/onlinechat/"+a+"/"+c+"/"+r),console.log("web socket 已开启"+a+c+r),document.querySelector("#chat_container [name=message]").addEventListener("input",u.focusAddEventliste),document.querySelector("#chat_container [name=message]").addEventListener("blur",u.blurAddEventliste),$(t).on("ws.onmessage",(function(t,e){if("_crm_focus"==e){const t=document.getElementById("bd");t.scrollTop=t.scrollHeight,$(".shu-ru").attr("style","opacity:1;position: absolute;z-index: 1;bottom: -18px;left: 48px;font-size: 12px;color: #888;"),clearTimeout(o),o=setTimeout((()=>{$(".shu-ru").attr("style","opacity:0;position: absolute;z-index: 1;bottom: -18px;left: 48px;font-size: 12px;color: #888;")}),1e3)}else"_crm_unfocus"==e&&$(".shu-ru").attr("style","opacity:0;position: absolute;z-index: 1;bottom: -18px;left: 48px;font-size: 12px;color: #888;");try{"tickt"==(e=$.parseJSON(e)).type&&e.message.sessionid!=l&&(u.holdon=!1,u.dialog(e.message.message,e.message.type,e.message.create_at),$(".new-tickits").removeClass("active"),window.Notification&&"default"==Notification.permission&&Notification.requestPermission(),$("#chat_container [name=message]").focus(),document.querySelector("#chat_container [name=message]").addEventListener("paste",(async t=>{if("file"==t.clipboardData.items[0].kind){t.preventDefault();const e=await s(t);/^data:image\/png;base64,/.test(e)?document.execCommand("insertImage",!1,e):document.execCommand("insertText",!1,e)}})),document.querySelector("#chat_container [name=message]").ondragstart=function(){return!1},$("#chat_container .content .body").dropzone({url:"/welcome/upload",previewTemplate:i,autoQueue:!0,timeout:6e4,maxFilesize:10,previewsContainer:"#chat_container .content .body",clickable:"[name=send_files]",success:function(t,e){if("success"==(e=$.parseJSON(e)).status){t.previewElement.remove();let i=new Date,n=i.getHours();n=n>=10?n:"0"+n;let o=i.getMinutes();o=o>=10?o:"0"+o;let s=n+":"+o;u.dialog(e.message,0,s)}else alert(e.message),t.previewElement.remove()},addedfiles:function(t){},sending:function(t,e,i){i.append("session_id",l),i.append("client_id",a)},drop:function(t,e){},maxfilesexceeded:function(){},error:function(t,e){alert(e),t.previewElement.remove()}}),document.querySelector("#chat_container .content .bd").onscroll=function(){0==$(this).scrollTop()&&u.state&&u.loadmore()},u.getCookie())}catch(t){console.log(t)}})),u.sendmsg(n);var m={};m.client_id=a,m.status="live",$.post("/welcome/sendStatus",m)}))}$(this).html("")}}})),$(document).on("click","#chat_container .content img",(function(){if(!$(this).hasClass("mdi-chat-emoji")){$(".online-float-image").length>0&&$(".online-float-image").remove();let t=$(this).get(0).outerHTML;t="<div class='online-float-image'><button name='close'><i class='submail-icon-close'></i></button>"+t+"</div>",$("body").append(t)}})),$(document).on("click",".online-float-image button[name=close]",(function(){$(this).hasClass("mdi-chat-emoji")||$(".online-float-image").length>0&&$(".online-float-image").remove()})),setInterval((function(){$(".animated-circles").hasClass("animated")?$(".animated-circles").removeClass("animated"):$(".animated-circles").addClass("animated")}),3e3)}));