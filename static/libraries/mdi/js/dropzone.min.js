"use strict";function _typeof(e){return(_typeof="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function e(t){return typeof t}:function e(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(e)}function _possibleConstructorReturn(e,t){return!t||"object"!==_typeof(t)&&"function"!=typeof t?_assertThisInitialized(e):t}function _getPrototypeOf(e){return(_getPrototypeOf=Object.setPrototypeOf?Object.getPrototypeOf:function e(t){return t.__proto__||Object.getPrototypeOf(t)})(e)}function _assertThisInitialized(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function _inherits(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),t&&_setPrototypeOf(e,t)}function _setPrototypeOf(e,t){return(_setPrototypeOf=Object.setPrototypeOf||function e(t,n){return t.__proto__=n,t})(e,t)}function _classCallCheck(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function _defineProperties(e,t){for(var n=0;n<t.length;n++){var i=t[n];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,i.key,i)}}function _createClass(e,t,n){return t&&_defineProperties(e.prototype,t),n&&_defineProperties(e,n),e}function __guard__(e,t){return null!=e?t(e):void 0}function __guardMethod__(e,t,n){return null!=e&&"function"==typeof e[t]?n(e,t):void 0}var Emitter=function(){function e(){_classCallCheck(this,e)}return _createClass(e,[{key:"on",value:function e(t,n){return this._callbacks=this._callbacks||{},this._callbacks[t]||(this._callbacks[t]=[]),this._callbacks[t].push(n),this}},{key:"emit",value:function e(t){this._callbacks=this._callbacks||{};var n=this._callbacks[t];if(n){for(var i=arguments.length,r=new Array(i>1?i-1:0),o=1;o<i;o++)r[o-1]=arguments[o];var a=!0,l=!1,s=void 0;try{for(var u=n[Symbol.iterator](),c;!(a=(c=u.next()).done);a=!0){var d=c.value;d.apply(this,r)}}catch(e){l=!0,s=e}finally{try{a||null==u.return||u.return()}finally{if(l)throw s}}}return this}},{key:"off",value:function e(t,n){if(!this._callbacks||0===arguments.length)return this._callbacks={},this;var i=this._callbacks[t];if(!i)return this;if(1===arguments.length)return delete this._callbacks[t],this;for(var r=0;r<i.length;r++){var o=i[r];if(o===n){i.splice(r,1);break}}return this}}]),e}(),Dropzone=function(e){function t(e,n){var i,r,o;if(_classCallCheck(this,t),(i=_possibleConstructorReturn(this,_getPrototypeOf(t).call(this))).element=e,i.version=t.version,i.defaultOptions.previewTemplate=i.defaultOptions.previewTemplate.replace(/\n*/g,""),i.clickableElements=[],i.listeners=[],i.files=[],"string"==typeof i.element&&(i.element=document.querySelector(i.element)),!i.element||null==i.element.nodeType)throw new Error("Invalid dropzone element.");if(i.element.dropzone)throw new Error("Dropzone already attached.");t.instances.push(_assertThisInitialized(i)),i.element.dropzone=_assertThisInitialized(i);var a=null!=(o=t.optionsForElement(i.element))?o:{};if(i.options=t.extend({},i.defaultOptions,a,null!=n?n:{}),i.options.forceFallback||!t.isBrowserSupported())return _possibleConstructorReturn(i,i.options.fallback.call(_assertThisInitialized(i)));if(null==i.options.url&&(i.options.url=i.element.getAttribute("action")),!i.options.url)throw new Error("No URL provided.");if(i.options.acceptedFiles&&i.options.acceptedMimeTypes)throw new Error("You can't provide both 'acceptedFiles' and 'acceptedMimeTypes'. 'acceptedMimeTypes' is deprecated.");if(i.options.uploadMultiple&&i.options.chunking)throw new Error("You cannot set both: uploadMultiple and chunking.");return i.options.acceptedMimeTypes&&(i.options.acceptedFiles=i.options.acceptedMimeTypes,delete i.options.acceptedMimeTypes),null!=i.options.renameFilename&&(i.options.renameFile=function(e){return i.options.renameFilename.call(_assertThisInitialized(i),e.name,e)}),i.options.method=i.options.method.toUpperCase(),(r=i.getExistingFallback())&&r.parentNode&&r.parentNode.removeChild(r),!1!==i.options.previewsContainer&&(i.options.previewsContainer?i.previewsContainer=t.getElement(i.options.previewsContainer,"previewsContainer"):i.previewsContainer=i.element),i.options.clickable&&(!0===i.options.clickable?i.clickableElements=[i.element]:i.clickableElements=t.getElements(i.options.clickable,"clickable")),i.init(),i}return _inherits(t,e),_createClass(t,null,[{key:"initClass",value:function e(){this.prototype.Emitter=Emitter,this.prototype.events=["drop","dragstart","dragend","dragenter","dragover","dragleave","addedfile","addedfiles","removedfile","thumbnail","error","errormultiple","processing","processingmultiple","uploadprogress","totaluploadprogress","sending","sendingmultiple","success","successmultiple","canceled","canceledmultiple","complete","completemultiple","reset","maxfilesexceeded","maxfilesreached","queuecomplete"],this.prototype.defaultOptions={url:null,method:"post",withCredentials:!1,timeout:3e4,parallelUploads:2,uploadMultiple:!1,chunking:!1,forceChunking:!1,chunkSize:2e6,parallelChunkUploads:!1,retryChunks:!1,retryChunksLimit:3,maxFilesize:256,paramName:"file",createImageThumbnails:!0,maxThumbnailFilesize:10,thumbnailWidth:120,thumbnailHeight:120,thumbnailMethod:"crop",resizeWidth:null,resizeHeight:null,resizeMimeType:null,resizeQuality:.8,resizeMethod:"contain",filesizeBase:1e3,maxFiles:null,headers:null,clickable:!0,ignoreHiddenFiles:!0,acceptedFiles:null,acceptedMimeTypes:null,autoProcessQueue:!0,autoQueue:!0,addRemoveLinks:!1,previewsContainer:null,hiddenInputContainer:"body",capture:null,renameFilename:null,renameFile:null,forceFallback:!1,dictDefaultMessage:"Drop files here to upload",dictFallbackMessage:"Your browser does not support drag'n'drop file uploads.",dictFallbackText:"Please use the fallback form below to upload your files like in the olden days.",dictFileTooBig:"File is too big ({{filesize}}MiB). Max filesize: {{maxFilesize}}MiB.",dictInvalidFileType:"You can't upload files of this type.",dictResponseError:"Server responded with {{statusCode}} code.",dictCancelUpload:"Cancel upload",dictUploadCanceled:"Upload canceled.",dictCancelUploadConfirmation:"Are you sure you want to cancel this upload?",dictRemoveFile:"Remove file",dictRemoveFileConfirmation:null,dictMaxFilesExceeded:"You can not upload any more files.",dictFileSizeUnits:{tb:"TB",gb:"GB",mb:"MB",kb:"KB",b:"b"},init:function e(){},params:function e(t,n,i){if(i)return{dzuuid:i.file.upload.uuid,dzchunkindex:i.index,dztotalfilesize:i.file.size,dzchunksize:this.options.chunkSize,dztotalchunkcount:i.file.upload.totalChunkCount,dzchunkbyteoffset:i.index*this.options.chunkSize}},accept:function e(t,n){return n()},chunksUploaded:function e(t,n){n()},fallback:function e(){var n;this.element.className="".concat(this.element.className," dz-browser-not-supported");var i=!0,r=!1,o=void 0;try{for(var a=this.element.getElementsByTagName("div")[Symbol.iterator](),l;!(i=(l=a.next()).done);i=!0){var s=l.value;if(/(^| )dz-message($| )/.test(s.className)){n=s,s.className="dz-message";break}}}catch(e){r=!0,o=e}finally{try{i||null==a.return||a.return()}finally{if(r)throw o}}n||(n=t.createElement('<div class="dz-message"><span></span></div>'),this.element.appendChild(n));var u=n.getElementsByTagName("span")[0];return u&&(null!=u.textContent?u.textContent=this.options.dictFallbackMessage:null!=u.innerText&&(u.innerText=this.options.dictFallbackMessage)),this.element.appendChild(this.getFallbackForm())},resize:function e(t,n,i,r){var o={srcX:0,srcY:0,srcWidth:t.width,srcHeight:t.height},a=t.width/t.height;null==n&&null==i?(n=o.srcWidth,i=o.srcHeight):null==n?n=i*a:null==i&&(i=n/a);var l=(n=Math.min(n,o.srcWidth))/(i=Math.min(i,o.srcHeight));if(o.srcWidth>n||o.srcHeight>i)if("crop"===r)a>l?(o.srcHeight=t.height,o.srcWidth=o.srcHeight*l):(o.srcWidth=t.width,o.srcHeight=o.srcWidth/l);else{if("contain"!==r)throw new Error("Unknown resizeMethod '".concat(r,"'"));a>l?i=n/a:n=i*a}return o.srcX=(t.width-o.srcWidth)/2,o.srcY=(t.height-o.srcHeight)/2,o.trgWidth=n,o.trgHeight=i,o},transformFile:function e(t,n){return(this.options.resizeWidth||this.options.resizeHeight)&&t.type.match(/image.*/)?this.resizeImage(t,this.options.resizeWidth,this.options.resizeHeight,this.options.resizeMethod,n):n(t)},previewTemplate:'<div class="dz-preview dz-file-preview">\n  <div class="dz-image"><img data-dz-thumbnail /></div>\n  <div class="dz-details">\n    <div class="dz-size"><span data-dz-size></span></div>\n    <div class="dz-filename"><span data-dz-name></span></div>\n  </div>\n  <div class="dz-progress"><span class="dz-upload" data-dz-uploadprogress></span></div>\n  <div class="dz-error-message"><span data-dz-errormessage></span></div>\n  <div class="dz-success-mark">\n    <svg width="54px" height="54px" viewBox="0 0 54 54" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">\n      <title>Check</title>\n      <g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">\n        <path d="M23.5,31.8431458 L17.5852419,25.9283877 C16.0248253,24.3679711 13.4910294,24.366835 11.9289322,25.9289322 C10.3700136,27.4878508 10.3665912,30.0234455 11.9283877,31.5852419 L20.4147581,40.0716123 C20.5133999,40.1702541 20.6159315,40.2626649 20.7218615,40.3488435 C22.2835669,41.8725651 24.794234,41.8626202 26.3461564,40.3106978 L43.3106978,23.3461564 C44.8771021,21.7797521 44.8758057,19.2483887 43.3137085,17.6862915 C41.7547899,16.1273729 39.2176035,16.1255422 37.6538436,17.6893022 L23.5,31.8431458 Z M27,53 C41.3594035,53 53,41.3594035 53,27 C53,12.6405965 41.3594035,1 27,1 C12.6405965,1 1,12.6405965 1,27 C1,41.3594035 12.6405965,53 27,53 Z" stroke-opacity="0.198794158" stroke="#747474" fill-opacity="0.816519475" fill="#FFFFFF"></path>\n      </g>\n    </svg>\n  </div>\n  <div class="dz-error-mark">\n    <svg width="54px" height="54px" viewBox="0 0 54 54" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">\n      <title>Error</title>\n      <g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">\n        <g stroke="#747474" stroke-opacity="0.198794158" fill="#FFFFFF" fill-opacity="0.816519475">\n          <path d="M32.6568542,29 L38.3106978,23.3461564 C39.8771021,21.7797521 39.8758057,19.2483887 38.3137085,17.6862915 C36.7547899,16.1273729 34.2176035,16.1255422 32.6538436,17.6893022 L27,23.3431458 L21.3461564,17.6893022 C19.7823965,16.1255422 17.2452101,16.1273729 15.6862915,17.6862915 C14.1241943,19.2483887 14.1228979,21.7797521 15.6893022,23.3461564 L21.3431458,29 L15.6893022,34.6538436 C14.1228979,36.2202479 14.1241943,38.7516113 15.6862915,40.3137085 C17.2452101,41.8726271 19.7823965,41.8744578 21.3461564,40.3106978 L27,34.6568542 L32.6538436,40.3106978 C34.2176035,41.8744578 36.7547899,41.8726271 38.3137085,40.3137085 C39.8758057,38.7516113 39.8771021,36.2202479 38.3106978,34.6538436 L32.6568542,29 Z M27,53 C41.3594035,53 53,41.3594035 53,27 C53,12.6405965 41.3594035,1 27,1 C12.6405965,1 1,12.6405965 1,27 C1,41.3594035 12.6405965,53 27,53 Z"></path>\n        </g>\n      </g>\n    </svg>\n  </div>\n</div>',drop:function e(t){return this.element.classList.remove("dz-drag-hover")},dragstart:function e(t){},dragend:function e(t){return this.element.classList.remove("dz-drag-hover")},dragenter:function e(t){return this.element.classList.add("dz-drag-hover")},dragover:function e(t){return this.element.classList.add("dz-drag-hover")},dragleave:function e(t){return this.element.classList.remove("dz-drag-hover")},paste:function e(t){},reset:function e(){return this.element.classList.remove("dz-started")},addedfile:function e(n){var i=this;if(this.element===this.previewsContainer&&this.element.classList.add("dz-started"),this.previewsContainer){n.previewElement=t.createElement(this.options.previewTemplate.trim()),n.previewTemplate=n.previewElement,this.previewsContainer.appendChild(n.previewElement);var r=!0,o=!1,a=void 0;try{for(var l=n.previewElement.querySelectorAll("[data-dz-name]")[Symbol.iterator](),s;!(r=(s=l.next()).done);r=!0){var u=s.value;u.textContent=n.name}}catch(e){o=!0,a=e}finally{try{r||null==l.return||l.return()}finally{if(o)throw a}}var c=!0,d=!1,p=void 0;try{for(var h=n.previewElement.querySelectorAll("[data-dz-size]")[Symbol.iterator](),f;!(c=(f=h.next()).done);c=!0)(u=f.value).innerHTML=this.filesize(n.size)}catch(e){d=!0,p=e}finally{try{c||null==h.return||h.return()}finally{if(d)throw p}}this.options.addRemoveLinks&&(n._removeLink=t.createElement('<a class="dz-remove" href="javascript:undefined;" data-dz-remove>'.concat(this.options.dictRemoveFile,"</a>")),n.previewElement.appendChild(n._removeLink));var v=function e(r){return r.preventDefault(),r.stopPropagation(),n.status===t.UPLOADING?t.confirm(i.options.dictCancelUploadConfirmation,(function(){return i.removeFile(n)})):i.options.dictRemoveFileConfirmation?t.confirm(i.options.dictRemoveFileConfirmation,(function(){return i.removeFile(n)})):i.removeFile(n)},m=!0,y=!1,g=void 0;try{for(var b=n.previewElement.querySelectorAll("[data-dz-remove]")[Symbol.iterator](),k;!(m=(k=b.next()).done);m=!0){var w;k.value.addEventListener("click",v)}}catch(e){y=!0,g=e}finally{try{m||null==b.return||b.return()}finally{if(y)throw g}}}},removedfile:function e(t){return null!=t.previewElement&&null!=t.previewElement.parentNode&&t.previewElement.parentNode.removeChild(t.previewElement),this._updateMaxFilesReachedClass()},thumbnail:function e(t,n){if(t.previewElement){t.previewElement.classList.remove("dz-file-preview");var i=!0,r=!1,o=void 0;try{for(var a=t.previewElement.querySelectorAll("[data-dz-thumbnail]")[Symbol.iterator](),l;!(i=(l=a.next()).done);i=!0){var s=l.value;s.alt=t.name,s.src=n}}catch(e){r=!0,o=e}finally{try{i||null==a.return||a.return()}finally{if(r)throw o}}return setTimeout((function(){return t.previewElement.classList.add("dz-image-preview")}),1)}},error:function e(t,n){if(t.previewElement){t.previewElement.classList.add("dz-error"),"String"!=typeof n&&n.error&&(n=n.error);var i=!0,r=!1,o=void 0;try{for(var a=t.previewElement.querySelectorAll("[data-dz-errormessage]")[Symbol.iterator](),l;!(i=(l=a.next()).done);i=!0){var s;l.value.textContent=n}}catch(e){r=!0,o=e}finally{try{i||null==a.return||a.return()}finally{if(r)throw o}}}},errormultiple:function e(){},processing:function e(t){if(t.previewElement&&(t.previewElement.classList.add("dz-processing"),t._removeLink))return t._removeLink.innerHTML=this.options.dictCancelUpload},processingmultiple:function e(){},uploadprogress:function e(t,n,i){if(t.previewElement){var r=!0,o=!1,a=void 0;try{for(var l=t.previewElement.querySelectorAll("[data-dz-uploadprogress]")[Symbol.iterator](),s;!(r=(s=l.next()).done);r=!0){var u=s.value;"PROGRESS"===u.nodeName?u.value=n:u.style.width="".concat(n,"%")}}catch(e){o=!0,a=e}finally{try{r||null==l.return||l.return()}finally{if(o)throw a}}}},totaluploadprogress:function e(){},sending:function e(){},sendingmultiple:function e(){},success:function e(t){if(t.previewElement)return t.previewElement.classList.add("dz-success")},successmultiple:function e(){},canceled:function e(t){return this.emit("error",t,this.options.dictUploadCanceled)},canceledmultiple:function e(){},complete:function e(t){if(t._removeLink&&(t._removeLink.innerHTML=this.options.dictRemoveFile),t.previewElement)return t.previewElement.classList.add("dz-complete")},completemultiple:function e(){},maxfilesexceeded:function e(){},maxfilesreached:function e(){},queuecomplete:function e(){},addedfiles:function e(){}},this.prototype._thumbnailQueue=[],this.prototype._processingThumbnail=!1}},{key:"extend",value:function e(t){for(var n=arguments.length,i=new Array(n>1?n-1:0),r=1;r<n;r++)i[r-1]=arguments[r];for(var o=0,a=i;o<a.length;o++){var l=a[o];for(var s in l){var u=l[s];t[s]=u}}return t}}]),_createClass(t,[{key:"getAcceptedFiles",value:function e(){return this.files.filter((function(e){return e.accepted})).map((function(e){return e}))}},{key:"getRejectedFiles",value:function e(){return this.files.filter((function(e){return!e.accepted})).map((function(e){return e}))}},{key:"getFilesWithStatus",value:function e(t){return this.files.filter((function(e){return e.status===t})).map((function(e){return e}))}},{key:"getQueuedFiles",value:function e(){return this.getFilesWithStatus(t.QUEUED)}},{key:"getUploadingFiles",value:function e(){return this.getFilesWithStatus(t.UPLOADING)}},{key:"getAddedFiles",value:function e(){return this.getFilesWithStatus(t.ADDED)}},{key:"getActiveFiles",value:function e(){return this.files.filter((function(e){return e.status===t.UPLOADING||e.status===t.QUEUED})).map((function(e){return e}))}},{key:"init",value:function e(){var n=this,i;("form"===this.element.tagName&&this.element.setAttribute("enctype","multipart/form-data"),this.element.classList.contains("dropzone")&&!this.element.querySelector(".dz-message")&&this.element.appendChild(t.createElement('<div class="dz-default dz-message"><button class="dz-button" type="button">'.concat(this.options.dictDefaultMessage,"</button></div>"))),this.clickableElements.length)&&function e(){return n.hiddenFileInput&&n.hiddenFileInput.parentNode.removeChild(n.hiddenFileInput),n.hiddenFileInput=document.createElement("input"),n.hiddenFileInput.setAttribute("type","file"),(null===n.options.maxFiles||n.options.maxFiles>1)&&n.hiddenFileInput.setAttribute("multiple","multiple"),n.hiddenFileInput.className="dz-hidden-input",null!==n.options.acceptedFiles&&n.hiddenFileInput.setAttribute("accept",n.options.acceptedFiles),null!==n.options.capture&&n.hiddenFileInput.setAttribute("capture",n.options.capture),n.hiddenFileInput.style.visibility="hidden",n.hiddenFileInput.style.position="absolute",n.hiddenFileInput.style.top="0",n.hiddenFileInput.style.left="0",n.hiddenFileInput.style.height="0",n.hiddenFileInput.style.width="0",t.getElement(n.options.hiddenInputContainer,"hiddenInputContainer").appendChild(n.hiddenFileInput),n.hiddenFileInput.addEventListener("change",(function(){var t=n.hiddenFileInput.files;if(t.length){var i=!0,r=!1,o=void 0;try{for(var a=t[Symbol.iterator](),l;!(i=(l=a.next()).done);i=!0){var s=l.value;n.addFile(s)}}catch(e){r=!0,o=e}finally{try{i||null==a.return||a.return()}finally{if(r)throw o}}}return n.emit("addedfiles",t),e()}))}();this.URL=null!==window.URL?window.URL:window.webkitURL;var r=!0,o=!1,a=void 0;try{for(var l=this.events[Symbol.iterator](),s;!(r=(s=l.next()).done);r=!0){var u=s.value;this.on(u,this.options[u])}}catch(e){o=!0,a=e}finally{try{r||null==l.return||l.return()}finally{if(o)throw a}}this.on("uploadprogress",(function(){return n.updateTotalUploadProgress()})),this.on("removedfile",(function(){return n.updateTotalUploadProgress()})),this.on("canceled",(function(e){return n.emit("complete",e)})),this.on("complete",(function(e){if(0===n.getAddedFiles().length&&0===n.getUploadingFiles().length&&0===n.getQueuedFiles().length)return setTimeout((function(){return n.emit("queuecomplete")}),0)}));var c=function e(t){return t.dataTransfer.types&&t.dataTransfer.types.some((function(e){return"Files"==e}))},d=function e(t){if(c(t))return t.stopPropagation(),t.preventDefault?t.preventDefault():t.returnValue=!1};return this.listeners=[{element:this.element,events:{dragstart:function e(t){return n.emit("dragstart",t)},dragenter:function e(t){return d(t),n.emit("dragenter",t)},dragover:function e(t){var i;try{i=t.dataTransfer.effectAllowed}catch(e){}return t.dataTransfer.dropEffect="move"===i||"linkMove"===i?"move":"copy",d(t),n.emit("dragover",t)},dragleave:function e(t){return n.emit("dragleave",t)},drop:function e(t){return d(t),n.drop(t)},dragend:function e(t){return n.emit("dragend",t)}}}],this.clickableElements.forEach((function(e){return n.listeners.push({element:e,events:{click:function i(r){return(e!==n.element||r.target===n.element||t.elementInside(r.target,n.element.querySelector(".dz-message")))&&n.hiddenFileInput.click(),!0}}})})),this.enable(),this.options.init.call(this)}},{key:"destroy",value:function e(){return this.disable(),this.removeAllFiles(!0),(null!=this.hiddenFileInput?this.hiddenFileInput.parentNode:void 0)&&(this.hiddenFileInput.parentNode.removeChild(this.hiddenFileInput),this.hiddenFileInput=null),delete this.element.dropzone,t.instances.splice(t.instances.indexOf(this),1)}},{key:"updateTotalUploadProgress",value:function e(){var t,n=0,i=0,r;if(this.getActiveFiles().length){var o=!0,a=!1,l=void 0;try{for(var s=this.getActiveFiles()[Symbol.iterator](),u;!(o=(u=s.next()).done);o=!0){var c=u.value;n+=c.upload.bytesSent,i+=c.upload.total}}catch(e){a=!0,l=e}finally{try{o||null==s.return||s.return()}finally{if(a)throw l}}t=100*n/i}else t=100;return this.emit("totaluploadprogress",t,i,n)}},{key:"_getParamName",value:function e(t){return"function"==typeof this.options.paramName?this.options.paramName(t):"".concat(this.options.paramName).concat(this.options.uploadMultiple?"[".concat(t,"]"):"")}},{key:"_renameFile",value:function e(t){return"function"!=typeof this.options.renameFile?t.name:this.options.renameFile(t)}},{key:"getFallbackForm",value:function e(){var n,i;if(n=this.getExistingFallback())return n;var r='<div class="dz-fallback">';this.options.dictFallbackText&&(r+="<p>".concat(this.options.dictFallbackText,"</p>")),r+='<input type="file" name="'.concat(this._getParamName(0),'" ').concat(this.options.uploadMultiple?'multiple="multiple"':void 0,' /><input type="submit" value="Upload!"></div>');var o=t.createElement(r);return"FORM"!==this.element.tagName?(i=t.createElement('<form action="'.concat(this.options.url,'" enctype="multipart/form-data" method="').concat(this.options.method,'"></form>'))).appendChild(o):(this.element.setAttribute("enctype","multipart/form-data"),this.element.setAttribute("method",this.options.method)),null!=i?i:o}},{key:"getExistingFallback",value:function e(){for(var t=function e(t){var n=!0,i=!1,r=void 0;try{for(var o=t[Symbol.iterator](),a;!(n=(a=o.next()).done);n=!0){var l=a.value;if(/(^| )fallback($| )/.test(l.className))return l}}catch(e){i=!0,r=e}finally{try{n||null==o.return||o.return()}finally{if(i)throw r}}},n=0,i=["div","form"];n<i.length;n++){var r=i[n],o;if(o=t(this.element.getElementsByTagName(r)))return o}}},{key:"setupEventListeners",value:function e(){return this.listeners.map((function(e){return function(){var t=[];for(var n in e.events){var i=e.events[n];t.push(e.element.addEventListener(n,i,!1))}return t}()}))}},{key:"removeEventListeners",value:function e(){return this.listeners.map((function(e){return function(){var t=[];for(var n in e.events){var i=e.events[n];t.push(e.element.removeEventListener(n,i,!1))}return t}()}))}},{key:"disable",value:function e(){var t=this;return this.clickableElements.forEach((function(e){return e.classList.remove("dz-clickable")})),this.removeEventListeners(),this.disabled=!0,this.files.map((function(e){return t.cancelUpload(e)}))}},{key:"enable",value:function e(){return delete this.disabled,this.clickableElements.forEach((function(e){return e.classList.add("dz-clickable")})),this.setupEventListeners()}},{key:"filesize",value:function e(t){var n=0,i="b";if(t>0){for(var r=["tb","gb","mb","kb","b"],o=0;o<r.length;o++){var a=r[o],l;if(t>=Math.pow(this.options.filesizeBase,4-o)/10){n=t/Math.pow(this.options.filesizeBase,4-o),i=a;break}}n=Math.round(10*n)/10}return"<strong>".concat(n,"</strong> ").concat(this.options.dictFileSizeUnits[i])}},{key:"_updateMaxFilesReachedClass",value:function e(){return null!=this.options.maxFiles&&this.getAcceptedFiles().length>=this.options.maxFiles?(this.getAcceptedFiles().length===this.options.maxFiles&&this.emit("maxfilesreached",this.files),this.element.classList.add("dz-max-files-reached")):this.element.classList.remove("dz-max-files-reached")}},{key:"drop",value:function e(t){if(t.dataTransfer){this.emit("drop",t);for(var n=[],i=0;i<t.dataTransfer.files.length;i++)n[i]=t.dataTransfer.files[i];if(n.length){var r=t.dataTransfer.items;r&&r.length&&null!=r[0].webkitGetAsEntry?this._addFilesFromItems(r):this.handleFiles(n)}this.emit("addedfiles",n)}}},{key:"paste",value:function e(t){if(null!=__guard__(null!=t?t.clipboardData:void 0,(function(e){return e.items}))){this.emit("paste",t);var n=t.clipboardData.items;return n.length?this._addFilesFromItems(n):void 0}}},{key:"handleFiles",value:function e(t){var n=!0,i=!1,r=void 0;try{for(var o=t[Symbol.iterator](),a;!(n=(a=o.next()).done);n=!0){var l=a.value;this.addFile(l)}}catch(e){i=!0,r=e}finally{try{n||null==o.return||o.return()}finally{if(i)throw r}}}},{key:"_addFilesFromItems",value:function e(t){var n=this;return function(){var e=[],i=!0,r=!1,o=void 0;try{for(var a=t[Symbol.iterator](),l;!(i=(l=a.next()).done);i=!0){var s=l.value,u;null!=s.webkitGetAsEntry&&(u=s.webkitGetAsEntry())?u.isFile?e.push(n.addFile(s.getAsFile())):u.isDirectory?e.push(n._addFilesFromDirectory(u,u.name)):e.push(void 0):null!=s.getAsFile&&(null==s.kind||"file"===s.kind)?e.push(n.addFile(s.getAsFile())):e.push(void 0)}}catch(e){r=!0,o=e}finally{try{i||null==a.return||a.return()}finally{if(r)throw o}}return e}()}},{key:"_addFilesFromDirectory",value:function e(t,n){var i=this,r=t.createReader(),o=function e(t){return __guardMethod__(console,"log",(function(e){return e.log(t)}))},a;return function e(){return r.readEntries((function(t){if(t.length>0){var r=!0,o=!1,a=void 0;try{for(var l=t[Symbol.iterator](),s;!(r=(s=l.next()).done);r=!0){var u=s.value;u.isFile?u.file((function(e){if(!i.options.ignoreHiddenFiles||"."!==e.name.substring(0,1))return e.fullPath="".concat(n,"/").concat(e.name),i.addFile(e)})):u.isDirectory&&i._addFilesFromDirectory(u,"".concat(n,"/").concat(u.name))}}catch(e){o=!0,a=e}finally{try{r||null==l.return||l.return()}finally{if(o)throw a}}e()}return null}),o)}()}},{key:"accept",value:function e(n,i){this.options.maxFilesize&&n.size>1024*this.options.maxFilesize*1024?i(this.options.dictFileTooBig.replace("{{filesize}}",Math.round(n.size/1024/10.24)/100).replace("{{maxFilesize}}",this.options.maxFilesize)):t.isValidFile(n,this.options.acceptedFiles)?null!=this.options.maxFiles&&this.getAcceptedFiles().length>=this.options.maxFiles?(i(this.options.dictMaxFilesExceeded.replace("{{maxFiles}}",this.options.maxFiles)),this.emit("maxfilesexceeded",n)):this.options.accept.call(this,n,i):i(this.options.dictInvalidFileType)}},{key:"addFile",value:function e(n){var i=this;n.upload={uuid:t.uuidv4(),progress:0,total:n.size,bytesSent:0,filename:this._renameFile(n)},this.files.push(n),n.status=t.ADDED,this.emit("addedfile",n),this._enqueueThumbnail(n),this.accept(n,(function(e){e?(n.accepted=!1,i._errorProcessing([n],e)):(n.accepted=!0,i.options.autoQueue&&i.enqueueFile(n)),i._updateMaxFilesReachedClass()}))}},{key:"enqueueFiles",value:function e(t){var n=!0,i=!1,r=void 0;try{for(var o=t[Symbol.iterator](),a;!(n=(a=o.next()).done);n=!0){var l=a.value;this.enqueueFile(l)}}catch(e){i=!0,r=e}finally{try{n||null==o.return||o.return()}finally{if(i)throw r}}return null}},{key:"enqueueFile",value:function e(n){var i=this;if(n.status!==t.ADDED||!0!==n.accepted)throw new Error("This file can't be queued because it has already been processed or was rejected.");if(n.status=t.QUEUED,this.options.autoProcessQueue)return setTimeout((function(){return i.processQueue()}),0)}},{key:"_enqueueThumbnail",value:function e(t){var n=this;if(this.options.createImageThumbnails&&t.type.match(/image.*/)&&t.size<=1024*this.options.maxThumbnailFilesize*1024)return this._thumbnailQueue.push(t),setTimeout((function(){return n._processThumbnailQueue()}),0)}},{key:"_processThumbnailQueue",value:function e(){var t=this;if(!this._processingThumbnail&&0!==this._thumbnailQueue.length){this._processingThumbnail=!0;var n=this._thumbnailQueue.shift();return this.createThumbnail(n,this.options.thumbnailWidth,this.options.thumbnailHeight,this.options.thumbnailMethod,!0,(function(e){return t.emit("thumbnail",n,e),t._processingThumbnail=!1,t._processThumbnailQueue()}))}}},{key:"removeFile",value:function e(n){if(n.status===t.UPLOADING&&this.cancelUpload(n),this.files=without(this.files,n),this.emit("removedfile",n),0===this.files.length)return this.emit("reset")}},{key:"removeAllFiles",value:function e(n){null==n&&(n=!1);var i=!0,r=!1,o=void 0;try{for(var a=this.files.slice()[Symbol.iterator](),l;!(i=(l=a.next()).done);i=!0){var s=l.value;(s.status!==t.UPLOADING||n)&&this.removeFile(s)}}catch(e){r=!0,o=e}finally{try{i||null==a.return||a.return()}finally{if(r)throw o}}return null}},{key:"resizeImage",value:function e(n,i,r,o,a){var l=this;return this.createThumbnail(n,i,r,o,!0,(function(e,i){if(null==i)return a(n);var r=l.options.resizeMimeType;null==r&&(r=n.type);var o=i.toDataURL(r,l.options.resizeQuality);return"image/jpeg"!==r&&"image/jpg"!==r||(o=ExifRestore.restore(n.dataURL,o)),a(t.dataURItoBlob(o))}))}},{key:"createThumbnail",value:function e(t,n,i,r,o,a){var l=this,s=new FileReader;s.onload=function(){t.dataURL=s.result,"image/svg+xml"!==t.type?l.createThumbnailFromUrl(t,n,i,r,o,a):null!=a&&a(s.result)},s.readAsDataURL(t)}},{key:"displayExistingFile",value:function e(t,n,i,r,o){var a=this,l=!(arguments.length>4&&void 0!==o)||o;if(this.emit("addedfile",t),this.emit("complete",t),l){var s=function e(n){a.emit("thumbnail",t,n),i&&i()};t.dataURL=n,this.createThumbnailFromUrl(t,this.options.thumbnailWidth,this.options.thumbnailHeight,this.options.resizeMethod,this.options.fixOrientation,s,r)}else this.emit("thumbnail",t,n),i&&i()}},{key:"createThumbnailFromUrl",value:function e(t,n,i,r,o,a,l){var s=this,u=document.createElement("img");return l&&(u.crossOrigin=l),u.onload=function(){var e=function e(t){return t(1)};return"undefined"!=typeof EXIF&&null!==EXIF&&o&&(e=function e(t){return EXIF.getData(u,(function(){return t(EXIF.getTag(this,"Orientation"))}))}),e((function(e){t.width=u.width,t.height=u.height;var o=s.options.resize.call(s,t,n,i,r),l=document.createElement("canvas"),c=l.getContext("2d");switch(l.width=o.trgWidth,l.height=o.trgHeight,e>4&&(l.width=o.trgHeight,l.height=o.trgWidth),e){case 2:c.translate(l.width,0),c.scale(-1,1);break;case 3:c.translate(l.width,l.height),c.rotate(Math.PI);break;case 4:c.translate(0,l.height),c.scale(1,-1);break;case 5:c.rotate(.5*Math.PI),c.scale(1,-1);break;case 6:c.rotate(.5*Math.PI),c.translate(0,-l.width);break;case 7:c.rotate(.5*Math.PI),c.translate(l.height,-l.width),c.scale(-1,1);break;case 8:c.rotate(-.5*Math.PI),c.translate(-l.height,0);break}drawImageIOSFix(c,u,null!=o.srcX?o.srcX:0,null!=o.srcY?o.srcY:0,o.srcWidth,o.srcHeight,null!=o.trgX?o.trgX:0,null!=o.trgY?o.trgY:0,o.trgWidth,o.trgHeight);var d=l.toDataURL("image/png");if(null!=a)return a(d,l)}))},null!=a&&(u.onerror=a),u.src=t.dataURL}},{key:"processQueue",value:function e(){var t=this.options.parallelUploads,n=this.getUploadingFiles().length,i=n;if(!(n>=t)){var r=this.getQueuedFiles();if(r.length>0){if(this.options.uploadMultiple)return this.processFiles(r.slice(0,t-n));for(;i<t;){if(!r.length)return;this.processFile(r.shift()),i++}}}}},{key:"processFile",value:function e(t){return this.processFiles([t])}},{key:"processFiles",value:function e(n){var i=!0,r=!1,o=void 0;try{for(var a=n[Symbol.iterator](),l;!(i=(l=a.next()).done);i=!0){var s=l.value;s.processing=!0,s.status=t.UPLOADING,this.emit("processing",s)}}catch(e){r=!0,o=e}finally{try{i||null==a.return||a.return()}finally{if(r)throw o}}return this.options.uploadMultiple&&this.emit("processingmultiple",n),this.uploadFiles(n)}},{key:"_getFilesWithXhr",value:function e(t){var n;return n=this.files.filter((function(e){return e.xhr===t})).map((function(e){return e}))}},{key:"cancelUpload",value:function e(n){if(n.status===t.UPLOADING){var i=this._getFilesWithXhr(n.xhr),r=!0,o=!1,a=void 0;try{for(var l=i[Symbol.iterator](),s;!(r=(s=l.next()).done);r=!0){var u;s.value.status=t.CANCELED}}catch(e){o=!0,a=e}finally{try{r||null==l.return||l.return()}finally{if(o)throw a}}
void 0!==n.xhr&&n.xhr.abort();var c=!0,d=!1,p=void 0;try{for(var h=i[Symbol.iterator](),f;!(c=(f=h.next()).done);c=!0){var v=f.value;this.emit("canceled",v)}}catch(e){d=!0,p=e}finally{try{c||null==h.return||h.return()}finally{if(d)throw p}}this.options.uploadMultiple&&this.emit("canceledmultiple",i)}else n.status!==t.ADDED&&n.status!==t.QUEUED||(n.status=t.CANCELED,this.emit("canceled",n),this.options.uploadMultiple&&this.emit("canceledmultiple",[n]));if(this.options.autoProcessQueue)return this.processQueue()}},{key:"resolveOption",value:function e(t){if("function"==typeof t){for(var n=arguments.length,i=new Array(n>1?n-1:0),r=1;r<n;r++)i[r-1]=arguments[r];return t.apply(this,i)}return t}},{key:"uploadFile",value:function e(t){return this.uploadFiles([t])}},{key:"uploadFiles",value:function e(n){var i=this;this._transformFiles(n,(function(e){if(i.options.chunking){var r=e[0];n[0].upload.chunked=i.options.chunking&&(i.options.forceChunking||r.size>i.options.chunkSize),n[0].upload.totalChunkCount=Math.ceil(r.size/i.options.chunkSize)}if(n[0].upload.chunked){var o=n[0],a=e[0],l=0;o.upload.chunks=[];var s=function e(){for(var r=0;void 0!==o.upload.chunks[r];)r++;if(!(r>=o.upload.totalChunkCount)){l++;var s=r*i.options.chunkSize,u=Math.min(s+i.options.chunkSize,o.size),c={name:i._getParamName(0),data:a.webkitSlice?a.webkitSlice(s,u):a.slice(s,u),filename:o.upload.filename,chunkIndex:r};o.upload.chunks[r]={file:o,index:r,dataBlock:c,status:t.UPLOADING,progress:0,retries:0},i._uploadData(n,[c])}};if(o.upload.finishedChunkUpload=function(e){var r=!0;e.status=t.SUCCESS,e.dataBlock=null,e.xhr=null;for(var a=0;a<o.upload.totalChunkCount;a++){if(void 0===o.upload.chunks[a])return s();o.upload.chunks[a].status!==t.SUCCESS&&(r=!1)}r&&i.options.chunksUploaded(o,(function(){i._finished(n,"",null)}))},i.options.parallelChunkUploads)for(var u=0;u<o.upload.totalChunkCount;u++)s();else s()}else{for(var c=[],d=0;d<n.length;d++)c[d]={name:i._getParamName(d),data:e[d],filename:n[d].upload.filename};i._uploadData(n,c)}}))}},{key:"_getChunk",value:function e(t,n){for(var i=0;i<t.upload.totalChunkCount;i++)if(void 0!==t.upload.chunks[i]&&t.upload.chunks[i].xhr===n)return t.upload.chunks[i]}},{key:"_uploadData",value:function e(n,i){var r=this,o=new XMLHttpRequest,a=!0,l=!1,s=void 0;try{for(var u=n[Symbol.iterator](),c;!(a=(c=u.next()).done);a=!0){var d;c.value.xhr=o}}catch(e){l=!0,s=e}finally{try{a||null==u.return||u.return()}finally{if(l)throw s}}n[0].upload.chunked&&(n[0].upload.chunks[i[0].chunkIndex].xhr=o);var p=this.resolveOption(this.options.method,n),h=this.resolveOption(this.options.url,n),f;o.open(p,h,!0),o.timeout=this.resolveOption(this.options.timeout,n),o.withCredentials=!!this.options.withCredentials,o.onload=function(e){r._finishedUploading(n,o,e)},o.ontimeout=function(){r._handleUploadError(n,o,"Request timedout after ".concat(r.options.timeout," seconds"))},o.onerror=function(){r._handleUploadError(n,o)},(null!=o.upload?o.upload:o).onprogress=function(e){return r._updateFilesUploadProgress(n,o,e)};var v={Accept:"application/json","Cache-Control":"no-cache","X-Requested-With":"XMLHttpRequest"};for(var m in this.options.headers&&t.extend(v,this.options.headers),v){var y=v[m];y&&o.setRequestHeader(m,y)}var g=new FormData;if(this.options.params){var b=this.options.params;for(var k in"function"==typeof b&&(b=b.call(this,n,o,n[0].upload.chunked?this._getChunk(n[0],o):null)),b){var w=b[k];g.append(k,w)}}var F=!0,z=!1,x=void 0;try{for(var E=n[Symbol.iterator](),C;!(F=(C=E.next()).done);F=!0){var _=C.value;this.emit("sending",_,o,g)}}catch(e){z=!0,x=e}finally{try{F||null==E.return||E.return()}finally{if(z)throw x}}this.options.uploadMultiple&&this.emit("sendingmultiple",n,o,g),this._addFormElementData(g);for(var S=0;S<i.length;S++){var D=i[S];g.append(D.name,D.data,D.filename)}this.submitRequest(o,g,n)}},{key:"_transformFiles",value:function e(t,n){for(var i=this,r=[],o=0,a=function e(a){i.options.transformFile.call(i,t[a],(function(e){r[a]=e,++o===t.length&&n(r)}))},l=0;l<t.length;l++)a(l)}},{key:"_addFormElementData",value:function e(t){if("FORM"===this.element.tagName){var n=!0,i=!1,r=void 0;try{for(var o=this.element.querySelectorAll("input, textarea, select, button")[Symbol.iterator](),a;!(n=(a=o.next()).done);n=!0){var l=a.value,s=l.getAttribute("name"),u=l.getAttribute("type");if(u&&(u=u.toLowerCase()),null!=s)if("SELECT"===l.tagName&&l.hasAttribute("multiple")){var c=!0,d=!1,p=void 0;try{for(var h=l.options[Symbol.iterator](),f;!(c=(f=h.next()).done);c=!0){var v=f.value;v.selected&&t.append(s,v.value)}}catch(e){d=!0,p=e}finally{try{c||null==h.return||h.return()}finally{if(d)throw p}}}else(!u||"checkbox"!==u&&"radio"!==u||l.checked)&&t.append(s,l.value)}}catch(e){i=!0,r=e}finally{try{n||null==o.return||o.return()}finally{if(i)throw r}}}}},{key:"_updateFilesUploadProgress",value:function e(t,n,i){var r;if(void 0!==i){if(r=100*i.loaded/i.total,t[0].upload.chunked){var o=t[0],a=this._getChunk(o,n);a.progress=r,a.total=i.total,a.bytesSent=i.loaded;var l=0,s,u;o.upload.progress=0,o.upload.total=0,o.upload.bytesSent=0;for(var c=0;c<o.upload.totalChunkCount;c++)void 0!==o.upload.chunks[c]&&void 0!==o.upload.chunks[c].progress&&(o.upload.progress+=o.upload.chunks[c].progress,o.upload.total+=o.upload.chunks[c].total,o.upload.bytesSent+=o.upload.chunks[c].bytesSent);o.upload.progress=o.upload.progress/o.upload.totalChunkCount}else{var d=!0,p=!1,h=void 0;try{for(var f=t[Symbol.iterator](),v;!(d=(v=f.next()).done);d=!0){var m=v.value;m.upload.progress=r,m.upload.total=i.total,m.upload.bytesSent=i.loaded}}catch(e){p=!0,h=e}finally{try{d||null==f.return||f.return()}finally{if(p)throw h}}}var y=!0,g=!1,b=void 0;try{for(var k=t[Symbol.iterator](),w;!(y=(w=k.next()).done);y=!0){var F=w.value;this.emit("uploadprogress",F,F.upload.progress,F.upload.bytesSent)}}catch(e){g=!0,b=e}finally{try{y||null==k.return||k.return()}finally{if(g)throw b}}}else{var z=!0;r=100;var x=!0,E=!1,C=void 0;try{for(var _=t[Symbol.iterator](),S;!(x=(S=_.next()).done);x=!0){var D=S.value;100===D.upload.progress&&D.upload.bytesSent===D.upload.total||(z=!1),D.upload.progress=r,D.upload.bytesSent=D.upload.total}}catch(e){E=!0,C=e}finally{try{x||null==_.return||_.return()}finally{if(E)throw C}}if(z)return;var T=!0,L=!1,A=void 0;try{for(var U=t[Symbol.iterator](),I;!(T=(I=U.next()).done);T=!0){var M=I.value;this.emit("uploadprogress",M,r,M.upload.bytesSent)}}catch(e){L=!0,A=e}finally{try{T||null==U.return||U.return()}finally{if(L)throw A}}}}},{key:"_finishedUploading",value:function e(n,i,r){var o;if(n[0].status!==t.CANCELED&&4===i.readyState){if("arraybuffer"!==i.responseType&&"blob"!==i.responseType&&(o=i.responseText,i.getResponseHeader("content-type")&&~i.getResponseHeader("content-type").indexOf("application/json")))try{o=JSON.parse(o)}catch(e){r=e,o="Invalid JSON response from server."}this._updateFilesUploadProgress(n),200<=i.status&&i.status<300?n[0].upload.chunked?n[0].upload.finishedChunkUpload(this._getChunk(n[0],i)):this._finished(n,o,r):this._handleUploadError(n,i,o)}}},{key:"_handleUploadError",value:function e(n,i,r){if(n[0].status!==t.CANCELED){if(n[0].upload.chunked&&this.options.retryChunks){var o=this._getChunk(n[0],i);if(o.retries++<this.options.retryChunksLimit)return void this._uploadData(n,[o.dataBlock]);console.warn("Retried this chunk too often. Giving up.")}this._errorProcessing(n,r||this.options.dictResponseError.replace("{{statusCode}}",i.status),i)}}},{key:"submitRequest",value:function e(t,n,i){t.send(n)}},{key:"_finished",value:function e(n,i,r){var o=!0,a=!1,l=void 0;try{for(var s=n[Symbol.iterator](),u;!(o=(u=s.next()).done);o=!0){var c=u.value;c.status=t.SUCCESS,this.emit("success",c,i,r),this.emit("complete",c)}}catch(e){a=!0,l=e}finally{try{o||null==s.return||s.return()}finally{if(a)throw l}}if(this.options.uploadMultiple&&(this.emit("successmultiple",n,i,r),this.emit("completemultiple",n)),this.options.autoProcessQueue)return this.processQueue()}},{key:"_errorProcessing",value:function e(n,i,r){var o=!0,a=!1,l=void 0;try{for(var s=n[Symbol.iterator](),u;!(o=(u=s.next()).done);o=!0){var c=u.value;c.status=t.ERROR,this.emit("error",c,i,r),this.emit("complete",c)}}catch(e){a=!0,l=e}finally{try{o||null==s.return||s.return()}finally{if(a)throw l}}if(this.options.uploadMultiple&&(this.emit("errormultiple",n,i,r),this.emit("completemultiple",n)),this.options.autoProcessQueue)return this.processQueue()}}],[{key:"uuidv4",value:function e(){return"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,(function(e){var t=16*Math.random()|0,n;return("x"===e?t:3&t|8).toString(16)}))}}]),t}(Emitter);Dropzone.initClass(),Dropzone.version="5.7.0",Dropzone.options={},Dropzone.optionsForElement=function(e){return e.getAttribute("id")?Dropzone.options[camelize(e.getAttribute("id"))]:void 0},Dropzone.instances=[],Dropzone.forElement=function(e){if("string"==typeof e&&(e=document.querySelector(e)),null==(null!=e?e.dropzone:void 0))throw new Error("No Dropzone found for given element. This is probably because you're trying to access it before Dropzone had the time to initialize. Use the `init` option to setup any additional observers on your Dropzone.");return e.dropzone},Dropzone.autoDiscover=!0,Dropzone.discover=function(){var e;if(document.querySelectorAll)e=document.querySelectorAll(".dropzone");else{e=[];var t=function t(n){return function(){var t=[],i=!0,r=!1,o=void 0;try{for(var a=n[Symbol.iterator](),l;!(i=(l=a.next()).done);i=!0){var s=l.value;/(^| )dropzone($| )/.test(s.className)?t.push(e.push(s)):t.push(void 0)}}catch(e){r=!0,o=e}finally{try{i||null==a.return||a.return()}finally{if(r)throw o}}return t}()};t(document.getElementsByTagName("div")),t(document.getElementsByTagName("form"))}return function(){var t=[],n=!0,i=!1,r=void 0;try{for(var o=e[Symbol.iterator](),a;!(n=(a=o.next()).done);n=!0){var l=a.value;!1!==Dropzone.optionsForElement(l)?t.push(new Dropzone(l)):t.push(void 0)}}catch(e){i=!0,r=e}finally{try{n||null==o.return||o.return()}finally{if(i)throw r}}return t}()},Dropzone.blacklistedBrowsers=[/opera.*(Macintosh|Windows Phone).*version\/12/i],Dropzone.isBrowserSupported=function(){var e=!0;if(window.File&&window.FileReader&&window.FileList&&window.Blob&&window.FormData&&document.querySelector)if("classList"in document.createElement("a")){var t=!0,n=!1,i=void 0;try{for(var r=Dropzone.blacklistedBrowsers[Symbol.iterator](),o;!(t=(o=r.next()).done);t=!0){var a;o.value.test(navigator.userAgent)&&(e=!1)}}catch(e){n=!0,i=e}finally{try{t||null==r.return||r.return()}finally{if(n)throw i}}}else e=!1;else e=!1;return e},Dropzone.dataURItoBlob=function(e){for(var t=atob(e.split(",")[1]),n=e.split(",")[0].split(":")[1].split(";")[0],i=new ArrayBuffer(t.length),r=new Uint8Array(i),o=0,a=t.length,l=0<=a;l?o<=a:o>=a;l?o++:o--)r[o]=t.charCodeAt(o);return new Blob([i],{type:n})};var without=function e(t,n){return t.filter((function(e){return e!==n})).map((function(e){return e}))},camelize=function e(t){return t.replace(/[\-_](\w)/g,(function(e){return e.charAt(1).toUpperCase()}))};Dropzone.createElement=function(e){var t=document.createElement("div");return t.innerHTML=e,t.childNodes[0]},Dropzone.elementInside=function(e,t){if(e===t)return!0;for(;e=e.parentNode;)if(e===t)return!0;return!1},Dropzone.getElement=function(e,t){var n;if("string"==typeof e?n=document.querySelector(e):null!=e.nodeType&&(n=e),null==n)throw new Error("Invalid `".concat(t,"` option provided. Please provide a CSS selector or a plain HTML element."));return n},Dropzone.getElements=function(e,t){var n,i;if(e instanceof Array){i=[];try{var r=!0,o=!1,a=void 0;try{for(var l=e[Symbol.iterator](),s;!(r=(s=l.next()).done);r=!0)n=s.value,i.push(this.getElement(n,t))}catch(e){o=!0,a=e}finally{try{r||null==l.return||l.return()}finally{if(o)throw a}}}catch(e){i=null}}else if("string"==typeof e){i=[];var u=!0,c=!1,d=void 0;try{for(var p=document.querySelectorAll(e)[Symbol.iterator](),h;!(u=(h=p.next()).done);u=!0)n=h.value,i.push(n)}catch(e){c=!0,d=e}finally{try{u||null==p.return||p.return()}finally{if(c)throw d}}}else null!=e.nodeType&&(i=[e]);if(null==i||!i.length)throw new Error("Invalid `".concat(t,"` option provided. Please provide a CSS selector, a plain HTML element or a list of those."));return i},Dropzone.confirm=function(e,t,n){return window.confirm(e)?t():null!=n?n():void 0},Dropzone.isValidFile=function(e,t){if(!t)return!0;t=t.split(",");var n=e.type,i=n.replace(/\/.*$/,""),r=!0,o=!1,a=void 0;try{for(var l=t[Symbol.iterator](),s;!(r=(s=l.next()).done);r=!0){var u=s.value;if("."===(u=u.trim()).charAt(0)){if(-1!==e.name.toLowerCase().indexOf(u.toLowerCase(),e.name.length-u.length))return!0}else if(/\/\*$/.test(u)){if(i===u.replace(/\/.*$/,""))return!0}else if(n===u)return!0}}catch(e){o=!0,a=e}finally{try{r||null==l.return||l.return()}finally{if(o)throw a}}return!1},"undefined"!=typeof jQuery&&null!==jQuery&&(jQuery.fn.dropzone=function(e){return this.each((function(){return new Dropzone(this,e)}))}),"undefined"!=typeof module&&null!==module?module.exports=Dropzone:window.Dropzone=Dropzone,Dropzone.ADDED="added",Dropzone.QUEUED="queued",Dropzone.ACCEPTED=Dropzone.QUEUED,Dropzone.UPLOADING="uploading",Dropzone.PROCESSING=Dropzone.UPLOADING,Dropzone.CANCELED="canceled",Dropzone.ERROR="error",Dropzone.SUCCESS="success";var detectVerticalSquash=function e(t){var n=t.naturalWidth,i=t.naturalHeight,r=document.createElement("canvas");r.width=1,r.height=i;var o=r.getContext("2d");o.drawImage(t,0,0);for(var a,l=o.getImageData(1,0,1,i).data,s=0,u=i,c=i;c>s;){var d;0===l[4*(c-1)+3]?u=c:s=c,c=u+s>>1}var p=c/i;return 0===p?1:p},drawImageIOSFix=function e(t,n,i,r,o,a,l,s,u,c){var d=detectVerticalSquash(n);return t.drawImage(n,i,r,o,a,l,s,u,c/d)},ExifRestore=function(){function e(){_classCallCheck(this,e)}return _createClass(e,null,[{key:"initClass",value:function e(){this.KEY_STR="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/="}},{key:"encode64",value:function e(t){for(var n="",i=void 0,r=void 0,o="",a=void 0,l=void 0,s=void 0,u="",c=0;a=(i=t[c++])>>2,l=(3&i)<<4|(r=t[c++])>>4,s=(15&r)<<2|(o=t[c++])>>6,u=63&o,isNaN(r)?s=u=64:isNaN(o)&&(u=64),n=n+this.KEY_STR.charAt(a)+this.KEY_STR.charAt(l)+this.KEY_STR.charAt(s)+this.KEY_STR.charAt(u),i=r=o="",a=l=s=u="",c<t.length;);return n}},{key:"restore",value:function e(t,n){if(!t.match("data:image/jpeg;base64,"))return n;var i=this.decode64(t.replace("data:image/jpeg;base64,","")),r=this.slice2Segments(i),o=this.exifManipulation(n,r);return"data:image/jpeg;base64,".concat(this.encode64(o))}},{key:"exifManipulation",value:function e(t,n){var i=this.getExifArray(n),r=this.insertExif(t,i),o;return new Uint8Array(r)}},{key:"getExifArray",value:function e(t){for(var n=void 0,i=0;i<t.length;){if(255===(n=t[i])[0]&225===n[1])return n;i++}return[]}},{key:"insertExif",value:function e(t,n){var i=t.replace("data:image/jpeg;base64,",""),r=this.decode64(i),o=r.indexOf(255,3),a=r.slice(0,o),l=r.slice(o),s=a;return s=(s=s.concat(n)).concat(l)}},{key:"slice2Segments",value:function e(t){for(var n=0,i=[];;){var r;if(255===t[n]&218===t[n+1])break;if(255===t[n]&216===t[n+1])n+=2;else{var o=n+(r=256*t[n+2]+t[n+3])+2,a=t.slice(n,o);i.push(a),n=o}if(n>t.length)break}return i}},{key:"decode64",value:function e(t){var n="",i=void 0,r=void 0,o="",a=void 0,l=void 0,s=void 0,u="",c=0,d=[],p;for(/[^A-Za-z0-9\+\/\=]/g.exec(t)&&console.warn("There were invalid base64 characters in the input text.\nValid base64 characters are A-Z, a-z, 0-9, '+', '/',and '='\nExpect errors in decoding."),t=t.replace(/[^A-Za-z0-9\+\/\=]/g,"");i=(a=this.KEY_STR.indexOf(t.charAt(c++)))<<2|(l=this.KEY_STR.indexOf(t.charAt(c++)))>>4,r=(15&l)<<4|(s=this.KEY_STR.indexOf(t.charAt(c++)))>>2,o=(3&s)<<6|(u=this.KEY_STR.indexOf(t.charAt(c++))),d.push(i),64!==s&&d.push(r),64!==u&&d.push(o),i=r=o="",a=l=s=u="",c<t.length;);return d}}]),e}();ExifRestore.initClass();var contentLoaded=function e(t,n){var i=!1,r=!0,o=t.document,a=o.documentElement,l=o.addEventListener?"addEventListener":"attachEvent",s=o.addEventListener?"removeEventListener":"detachEvent",u=o.addEventListener?"":"on",c=function e(r){if("readystatechange"!==r.type||"complete"===o.readyState)return("load"===r.type?t:o)[s](u+r.type,e,!1),!i&&(i=!0)?n.call(t,r.type||r):void 0},d=function e(){try{a.doScroll("left")}catch(t){return void setTimeout(e,50)}return c("poll")};if("complete"!==o.readyState){if(o.createEventObject&&a.doScroll){try{r=!t.frameElement}catch(e){}r&&d()}return o[l](u+"DOMContentLoaded",c,!1),o[l](u+"readystatechange",c,!1),t[l](u+"load",c,!1)}};Dropzone._autoDiscoverFunction=function(){if(Dropzone.autoDiscover)return Dropzone.discover()},contentLoaded(window,Dropzone._autoDiscoverFunction);