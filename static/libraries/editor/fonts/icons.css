@font-face {
  font-family: 'submail-icon-v2';
  src:  url('fonts/submail-icon-v2.eot');
  src:  url('fonts/submail-icon-v2.eot') format('embedded-opentype'),
    url('fonts/submail-icon-v2.ttf') format('truetype'),
    url('fonts/submail-icon-v2.woff') format('woff'),
    url('fonts/submail-icon-v2.svg') format('svg');
  font-weight: normal;
  font-style: normal;
}

[class^="submail-v2-"], [class*=" submail-v2-"] {
  /* use !important to prevent issues with browser extensions that change fonts */
  font-family: 'submail-icon-v2' !important;
  speak: none;
  font-style: normal;
  font-weight: normal;
  font-variant: normal;
  text-transform: none;
  line-height: 1;

  /* Better Font Rendering =========== */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.submail-v2-shorturl:before {
  content: "\e9f9";
}
.submail-v2-sms:before {
  content: "\e9fa";
}
.submail-v2-flashsms:before {
  content: "\e9f7";
}
.submail-v2-mms:before {
  content: "\e9f8";
}
.submail-v2-image .path1:before {
  content: "\e9c6";
  color: ;
}
.submail-v2-image .path2:before {
  content: "\e9c9";
  margin-left: -1.66015625em;
  color: ;
}
.submail-v2-style:before {
  content: "\e9ca";
}
.submail-v2-attributes:before {
  content: "\e91b";
}
.submail-v2-back:before {
  content: "\e91c";
}
.submail-v2-color:before {
  content: "\e921";
}
.submail-v2-desktop1:before {
  content: "\e924";
}
.submail-v2-elements:before {
  content: "\e931";
}
.submail-v2-image-library:before {
  content: "\e966";
}
.submail-v2-mobile:before {
  content: "\e967";
}
.submail-v2-save:before {
  content: "\e968";
}
.submail-v2-send-line:before {
  content: "\e96d";
}
.submail-v2-send:before {
  content: "\e96e";
}
.submail-v2-home:before {
  content: "\e600";
}
.submail-v2-home2:before {
  content: "\e601";
}
.submail-v2-home3:before {
  content: "\e602";
}
.submail-v2-home4:before {
  content: "\e603";
}
.submail-v2-home5:before {
  content: "\e604";
}
.submail-v2-home6:before {
  content: "\e605";
}
.submail-v2-bathtub:before {
  content: "\e606";
}
.submail-v2-toothbrush:before {
  content: "\e607";
}
.submail-v2-bed:before {
  content: "\e608";
}
.submail-v2-couch:before {
  content: "\e609";
}
.submail-v2-chair:before {
  content: "\e60a";
}
.submail-v2-city:before {
  content: "\e60b";
}
.submail-v2-apartment:before {
  content: "\e60c";
}
.submail-v2-pencil:before {
  content: "\e60d";
}
.submail-v2-pencil2:before {
  content: "\e60e";
}
.submail-v2-pen:before {
  content: "\e60f";
}
.submail-v2-pencil3:before {
  content: "\e610";
}
.submail-v2-eraser:before {
  content: "\e611";
}
.submail-v2-pencil4:before {
  content: "\e612";
}
.submail-v2-pencil5:before {
  content: "\e613";
}
.submail-v2-feather:before {
  content: "\e614";
}
.submail-v2-feather2:before {
  content: "\e615";
}
.submail-v2-feather3:before {
  content: "\e616";
}
.submail-v2-pen2:before {
  content: "\e617";
}
.submail-v2-pen-add:before {
  content: "\e618";
}
.submail-v2-pen-remove:before {
  content: "\e619";
}
.submail-v2-vector:before {
  content: "\e61a";
}
.submail-v2-pen3:before {
  content: "\e61b";
}
.submail-v2-blog:before {
  content: "\e61c";
}
.submail-v2-brush:before {
  content: "\e61d";
}
.submail-v2-brush2:before {
  content: "\e61e";
}
.submail-v2-spray:before {
  content: "\e61f";
}
.submail-v2-paint-roller:before {
  content: "\e620";
}
.submail-v2-stamp:before {
  content: "\e621";
}
.submail-v2-tape:before {
  content: "\e622";
}
.submail-v2-desk-tape:before {
  content: "\e623";
}
.submail-v2-texture:before {
  content: "\e624";
}
.submail-v2-eye-dropper:before {
  content: "\e625";
}
.submail-v2-palette:before {
  content: "\e626";
}
.submail-v2-color-sampler:before {
  content: "\e627";
}
.submail-v2-bucket:before {
  content: "\e628";
}
.submail-v2-gradient:before {
  content: "\e629";
}
.submail-v2-gradient2:before {
  content: "\e62a";
}
.submail-v2-magic-wand:before {
  content: "\e62b";
}
.submail-v2-magnet:before {
  content: "\e62c";
}
.submail-v2-pencil-ruler:before {
  content: "\e62d";
}
.submail-v2-pencil-ruler2:before {
  content: "\e62e";
}
.submail-v2-compass:before {
  content: "\e62f";
}
.submail-v2-aim:before {
  content: "\e630";
}
.submail-v2-gun:before {
  content: "\e631";
}
.submail-v2-bottle:before {
  content: "\e632";
}
.submail-v2-drop:before {
  content: "\e633";
}
.submail-v2-drop-crossed:before {
  content: "\e634";
}
.submail-v2-drop2:before {
  content: "\e635";
}
.submail-v2-snow:before {
  content: "\e636";
}
.submail-v2-snow2:before {
  content: "\e637";
}
.submail-v2-fire:before {
  content: "\e638";
}
.submail-v2-lighter:before {
  content: "\e639";
}
.submail-v2-knife:before {
  content: "\e63a";
}
.submail-v2-dagger:before {
  content: "\e63b";
}
.submail-v2-tissue:before {
  content: "\e63c";
}
.submail-v2-toilet-paper:before {
  content: "\e63d";
}
.submail-v2-poop:before {
  content: "\e63e";
}
.submail-v2-umbrella:before {
  content: "\e63f";
}
.submail-v2-umbrella2:before {
  content: "\e640";
}
.submail-v2-rain:before {
  content: "\e641";
}
.submail-v2-tornado:before {
  content: "\e642";
}
.submail-v2-wind:before {
  content: "\e643";
}
.submail-v2-fan:before {
  content: "\e644";
}
.submail-v2-contrast:before {
  content: "\e645";
}
.submail-v2-sun-small:before {
  content: "\e646";
}
.submail-v2-sun:before {
  content: "\e647";
}
.submail-v2-sun2:before {
  content: "\e648";
}
.submail-v2-moon:before {
  content: "\e649";
}
.submail-v2-cloud:before {
  content: "\e64a";
}
.submail-v2-cloud-upload:before {
  content: "\e64b";
}
.submail-v2-cloud-download:before {
  content: "\e64c";
}
.submail-v2-cloud-rain:before {
  content: "\e64d";
}
.submail-v2-cloud-hailstones:before {
  content: "\e64e";
}
.submail-v2-cloud-snow:before {
  content: "\e64f";
}
.submail-v2-cloud-windy:before {
  content: "\e650";
}
.submail-v2-sun-wind:before {
  content: "\e651";
}
.submail-v2-cloud-fog:before {
  content: "\e652";
}
.submail-v2-cloud-sun:before {
  content: "\e653";
}
.submail-v2-cloud-lightning:before {
  content: "\e654";
}
.submail-v2-cloud-sync:before {
  content: "\e655";
}
.submail-v2-cloud-lock:before {
  content: "\e656";
}
.submail-v2-cloud-gear:before {
  content: "\e657";
}
.submail-v2-cloud-alert:before {
  content: "\e658";
}
.submail-v2-cloud-check:before {
  content: "\e659";
}
.submail-v2-cloud-cross:before {
  content: "\e65a";
}
.submail-v2-cloud-crossed:before {
  content: "\e65b";
}
.submail-v2-cloud-database:before {
  content: "\e65c";
}
.submail-v2-database:before {
  content: "\e65d";
}
.submail-v2-database-add:before {
  content: "\e65e";
}
.submail-v2-database-remove:before {
  content: "\e65f";
}
.submail-v2-database-lock:before {
  content: "\e660";
}
.submail-v2-database-refresh:before {
  content: "\e661";
}
.submail-v2-database-check:before {
  content: "\e662";
}
.submail-v2-database-history:before {
  content: "\e663";
}
.submail-v2-database-upload:before {
  content: "\e664";
}
.submail-v2-database-download:before {
  content: "\e665";
}
.submail-v2-server:before {
  content: "\e666";
}
.submail-v2-shield:before {
  content: "\e667";
}
.submail-v2-shield-check:before {
  content: "\e668";
}
.submail-v2-shield-alert:before {
  content: "\e669";
}
.submail-v2-shield-cross:before {
  content: "\e66a";
}
.submail-v2-lock:before {
  content: "\e66b";
}
.submail-v2-rotation-lock:before {
  content: "\e66c";
}
.submail-v2-unlock:before {
  content: "\e66d";
}
.submail-v2-key:before {
  content: "\e66e";
}
.submail-v2-key-hole:before {
  content: "\e66f";
}
.submail-v2-toggle-off:before {
  content: "\e670";
}
.submail-v2-toggle-on:before {
  content: "\e671";
}
.submail-v2-cog:before {
  content: "\e672";
}
.submail-v2-cog2:before {
  content: "\e673";
}
.submail-v2-wrench:before {
  content: "\e674";
}
.submail-v2-screwdriver:before {
  content: "\e675";
}
.submail-v2-hammer-wrench:before {
  content: "\e676";
}
.submail-v2-hammer:before {
  content: "\e677";
}
.submail-v2-saw:before {
  content: "\e678";
}
.submail-v2-axe:before {
  content: "\e679";
}
.submail-v2-axe2:before {
  content: "\e67a";
}
.submail-v2-shovel:before {
  content: "\e67b";
}
.submail-v2-pickaxe:before {
  content: "\e67c";
}
.submail-v2-factory:before {
  content: "\e67d";
}
.submail-v2-factory2:before {
  content: "\e67e";
}
.submail-v2-recycle:before {
  content: "\e67f";
}
.submail-v2-trash:before {
  content: "\e680";
}
.submail-v2-trash2:before {
  content: "\e681";
}
.submail-v2-trash3:before {
  content: "\e682";
}
.submail-v2-broom:before {
  content: "\e683";
}
.submail-v2-game:before {
  content: "\e684";
}
.submail-v2-gamepad:before {
  content: "\e685";
}
.submail-v2-joystick:before {
  content: "\e686";
}
.submail-v2-dice:before {
  content: "\e687";
}
.submail-v2-spades:before {
  content: "\e688";
}
.submail-v2-diamonds:before {
  content: "\e689";
}
.submail-v2-clubs:before {
  content: "\e68a";
}
.submail-v2-hearts:before {
  content: "\e68b";
}
.submail-v2-heart:before {
  content: "\e68c";
}
.submail-v2-star:before {
  content: "\e68d";
}
.submail-v2-star-half:before {
  content: "\e68e";
}
.submail-v2-star-empty:before {
  content: "\e68f";
}
.submail-v2-flag:before {
  content: "\e690";
}
.submail-v2-flag2:before {
  content: "\e691";
}
.submail-v2-flag3:before {
  content: "\e692";
}
.submail-v2-mailbox-full:before {
  content: "\e693";
}
.submail-v2-mailbox-empty:before {
  content: "\e694";
}
.submail-v2-at-sign:before {
  content: "\e695";
}
.submail-v2-envelope:before {
  content: "\e696";
}
.submail-v2-envelope-open:before {
  content: "\e697";
}
.submail-v2-paperclip:before {
  content: "\e698";
}
.submail-v2-paper-plane:before {
  content: "\e699";
}
.submail-v2-reply:before {
  content: "\e69a";
}
.submail-v2-reply-all:before {
  content: "\e69b";
}
.submail-v2-inbox:before {
  content: "\e69c";
}
.submail-v2-inbox2:before {
  content: "\e69d";
}
.submail-v2-outbox:before {
  content: "\e69e";
}
.submail-v2-box:before {
  content: "\e69f";
}
.submail-v2-archive:before {
  content: "\e6a0";
}
.submail-v2-archive2:before {
  content: "\e6a1";
}
.submail-v2-drawers:before {
  content: "\e6a2";
}
.submail-v2-drawers2:before {
  content: "\e6a3";
}
.submail-v2-drawers3:before {
  content: "\e6a4";
}
.submail-v2-eye:before {
  content: "\e6a5";
}
.submail-v2-eye-crossed:before {
  content: "\e6a6";
}
.submail-v2-eye-plus:before {
  content: "\e6a7";
}
.submail-v2-eye-minus:before {
  content: "\e6a8";
}
.submail-v2-binoculars:before {
  content: "\e6a9";
}
.submail-v2-binoculars2:before {
  content: "\e6aa";
}
.submail-v2-hdd:before {
  content: "\e6ab";
}
.submail-v2-hdd-down:before {
  content: "\e6ac";
}
.submail-v2-hdd-up:before {
  content: "\e6ad";
}
.submail-v2-floppy-disk:before {
  content: "\e6ae";
}
.submail-v2-disc:before {
  content: "\e6af";
}
.submail-v2-tape2:before {
  content: "\e6b0";
}
.submail-v2-printer:before {
  content: "\e6b1";
}
.submail-v2-shredder:before {
  content: "\e6b2";
}
.submail-v2-file-empty:before {
  content: "\e6b3";
}
.submail-v2-file-add:before {
  content: "\e6b4";
}
.submail-v2-file-check:before {
  content: "\e6b5";
}
.submail-v2-file-lock:before {
  content: "\e6b6";
}
.submail-v2-files:before {
  content: "\e6b7";
}
.submail-v2-copy:before {
  content: "\e6b8";
}
.submail-v2-compare:before {
  content: "\e6b9";
}
.submail-v2-folder:before {
  content: "\e6ba";
}
.submail-v2-folder-search:before {
  content: "\e6bb";
}
.submail-v2-folder-plus:before {
  content: "\e6bc";
}
.submail-v2-folder-minus:before {
  content: "\e6bd";
}
.submail-v2-folder-download:before {
  content: "\e6be";
}
.submail-v2-folder-upload:before {
  content: "\e6bf";
}
.submail-v2-folder-star:before {
  content: "\e6c0";
}
.submail-v2-folder-heart:before {
  content: "\e6c1";
}
.submail-v2-folder-user:before {
  content: "\e6c2";
}
.submail-v2-folder-shared:before {
  content: "\e6c3";
}
.submail-v2-folder-music:before {
  content: "\e6c4";
}
.submail-v2-folder-picture:before {
  content: "\e6c5";
}
.submail-v2-folder-film:before {
  content: "\e6c6";
}
.submail-v2-scissors:before {
  content: "\e6c7";
}
.submail-v2-paste:before {
  content: "\e6c8";
}
.submail-v2-clipboard-empty:before {
  content: "\e6c9";
}
.submail-v2-clipboard-pencil:before {
  content: "\e6ca";
}
.submail-v2-clipboard-text:before {
  content: "\e6cb";
}
.submail-v2-clipboard-check:before {
  content: "\e6cc";
}
.submail-v2-clipboard-down:before {
  content: "\e6cd";
}
.submail-v2-clipboard-left:before {
  content: "\e6ce";
}
.submail-v2-clipboard-alert:before {
  content: "\e6cf";
}
.submail-v2-clipboard-user:before {
  content: "\e6d0";
}
.submail-v2-register:before {
  content: "\e6d1";
}
.submail-v2-enter:before {
  content: "\e6d2";
}
.submail-v2-exit:before {
  content: "\e6d3";
}
.submail-v2-papers:before {
  content: "\e6d4";
}
.submail-v2-news:before {
  content: "\e6d5";
}
.submail-v2-reading:before {
  content: "\e6d6";
}
.submail-v2-typewriter:before {
  content: "\e6d7";
}
.submail-v2-document:before {
  content: "\e6d8";
}
.submail-v2-document2:before {
  content: "\e6d9";
}
.submail-v2-graduation-hat:before {
  content: "\e6da";
}
.submail-v2-license:before {
  content: "\e6db";
}
.submail-v2-license2:before {
  content: "\e6dc";
}
.submail-v2-medal-empty:before {
  content: "\e6dd";
}
.submail-v2-medal-first:before {
  content: "\e6de";
}
.submail-v2-medal-second:before {
  content: "\e6df";
}
.submail-v2-medal-third:before {
  content: "\e6e0";
}
.submail-v2-podium:before {
  content: "\e6e1";
}
.submail-v2-trophy:before {
  content: "\e6e2";
}
.submail-v2-trophy2:before {
  content: "\e6e3";
}
.submail-v2-music-note:before {
  content: "\e6e4";
}
.submail-v2-music-note2:before {
  content: "\e6e5";
}
.submail-v2-music-note3:before {
  content: "\e6e6";
}
.submail-v2-playlist:before {
  content: "\e6e7";
}
.submail-v2-playlist-add:before {
  content: "\e6e8";
}
.submail-v2-guitar:before {
  content: "\e6e9";
}
.submail-v2-trumpet:before {
  content: "\e6ea";
}
.submail-v2-album:before {
  content: "\e6eb";
}
.submail-v2-shuffle:before {
  content: "\e6ec";
}
.submail-v2-repeat-one:before {
  content: "\e6ed";
}
.submail-v2-repeat:before {
  content: "\e6ee";
}
.submail-v2-headphones:before {
  content: "\e6ef";
}
.submail-v2-headset:before {
  content: "\e6f0";
}
.submail-v2-loudspeaker:before {
  content: "\e6f1";
}
.submail-v2-equalizer:before {
  content: "\e6f2";
}
.submail-v2-theater:before {
  content: "\e6f3";
}
.submail-v2-3d-glasses:before {
  content: "\e6f4";
}
.submail-v2-ticket:before {
  content: "\e6f5";
}
.submail-v2-presentation:before {
  content: "\e6f6";
}
.submail-v2-play:before {
  content: "\e6f7";
}
.submail-v2-film-play:before {
  content: "\e6f8";
}
.submail-v2-clapboard-play:before {
  content: "\e6f9";
}
.submail-v2-media:before {
  content: "\e6fa";
}
.submail-v2-film:before {
  content: "\e6fb";
}
.submail-v2-film2:before {
  content: "\e6fc";
}
.submail-v2-surveillance:before {
  content: "\e6fd";
}
.submail-v2-surveillance2:before {
  content: "\e6fe";
}
.submail-v2-camera:before {
  content: "\e6ff";
}
.submail-v2-camera-crossed:before {
  content: "\e700";
}
.submail-v2-camera-play:before {
  content: "\e701";
}
.submail-v2-time-lapse:before {
  content: "\e702";
}
.submail-v2-record:before {
  content: "\e703";
}
.submail-v2-camera2:before {
  content: "\e704";
}
.submail-v2-camera-flip:before {
  content: "\e705";
}
.submail-v2-panorama:before {
  content: "\e706";
}
.submail-v2-time-lapse2:before {
  content: "\e707";
}
.submail-v2-shutter:before {
  content: "\e708";
}
.submail-v2-shutter2:before {
  content: "\e709";
}
.submail-v2-face-detection:before {
  content: "\e70a";
}
.submail-v2-flare:before {
  content: "\e70b";
}
.submail-v2-convex:before {
  content: "\e70c";
}
.submail-v2-concave:before {
  content: "\e70d";
}
.submail-v2-picture:before {
  content: "\e70e";
}
.submail-v2-picture2:before {
  content: "\e70f";
}
.submail-v2-picture3:before {
  content: "\e710";
}
.submail-v2-pictures:before {
  content: "\e711";
}
.submail-v2-book:before {
  content: "\e712";
}
.submail-v2-audio-book:before {
  content: "\e713";
}
.submail-v2-book2:before {
  content: "\e714";
}
.submail-v2-bookmark:before {
  content: "\e715";
}
.submail-v2-bookmark2:before {
  content: "\e716";
}
.submail-v2-label:before {
  content: "\e717";
}
.submail-v2-library:before {
  content: "\e718";
}
.submail-v2-library2:before {
  content: "\e719";
}
.submail-v2-contacts:before {
  content: "\e71a";
}
.submail-v2-profile:before {
  content: "\e71b";
}
.submail-v2-portrait:before {
  content: "\e71c";
}
.submail-v2-portrait2:before {
  content: "\e71d";
}
.submail-v2-user:before {
  content: "\e71e";
}
.submail-v2-user-plus:before {
  content: "\e71f";
}
.submail-v2-user-minus:before {
  content: "\e720";
}
.submail-v2-user-lock:before {
  content: "\e721";
}
.submail-v2-users:before {
  content: "\e722";
}
.submail-v2-users2:before {
  content: "\e723";
}
.submail-v2-users-plus:before {
  content: "\e724";
}
.submail-v2-users-minus:before {
  content: "\e725";
}
.submail-v2-group-work:before {
  content: "\e726";
}
.submail-v2-woman:before {
  content: "\e727";
}
.submail-v2-man:before {
  content: "\e728";
}
.submail-v2-baby:before {
  content: "\e729";
}
.submail-v2-baby2:before {
  content: "\e72a";
}
.submail-v2-baby3:before {
  content: "\e72b";
}
.submail-v2-baby-bottle:before {
  content: "\e72c";
}
.submail-v2-walk:before {
  content: "\e72d";
}
.submail-v2-hand-waving:before {
  content: "\e72e";
}
.submail-v2-jump:before {
  content: "\e72f";
}
.submail-v2-run:before {
  content: "\e730";
}
.submail-v2-woman2:before {
  content: "\e731";
}
.submail-v2-man2:before {
  content: "\e732";
}
.submail-v2-man-woman:before {
  content: "\e733";
}
.submail-v2-height:before {
  content: "\e734";
}
.submail-v2-weight:before {
  content: "\e735";
}
.submail-v2-scale:before {
  content: "\e736";
}
.submail-v2-button:before {
  content: "\e737";
}
.submail-v2-bow-tie:before {
  content: "\e738";
}
.submail-v2-tie:before {
  content: "\e739";
}
.submail-v2-socks:before {
  content: "\e73a";
}
.submail-v2-shoe:before {
  content: "\e73b";
}
.submail-v2-shoes:before {
  content: "\e73c";
}
.submail-v2-hat:before {
  content: "\e73d";
}
.submail-v2-pants:before {
  content: "\e73e";
}
.submail-v2-shorts:before {
  content: "\e73f";
}
.submail-v2-flip-flops:before {
  content: "\e740";
}
.submail-v2-shirt:before {
  content: "\e741";
}
.submail-v2-hanger:before {
  content: "\e742";
}
.submail-v2-laundry:before {
  content: "\e743";
}
.submail-v2-store:before {
  content: "\e744";
}
.submail-v2-haircut:before {
  content: "\e745";
}
.submail-v2-store-24:before {
  content: "\e746";
}
.submail-v2-barcode:before {
  content: "\e747";
}
.submail-v2-barcode2:before {
  content: "\e748";
}
.submail-v2-barcode3:before {
  content: "\e749";
}
.submail-v2-cashier:before {
  content: "\e74a";
}
.submail-v2-bag:before {
  content: "\e74b";
}
.submail-v2-bag2:before {
  content: "\e74c";
}
.submail-v2-cart:before {
  content: "\e74d";
}
.submail-v2-cart-empty:before {
  content: "\e74e";
}
.submail-v2-cart-full:before {
  content: "\e74f";
}
.submail-v2-cart-plus:before {
  content: "\e750";
}
.submail-v2-cart-plus2:before {
  content: "\e751";
}
.submail-v2-cart-add:before {
  content: "\e752";
}
.submail-v2-cart-remove:before {
  content: "\e753";
}
.submail-v2-cart-exchange:before {
  content: "\e754";
}
.submail-v2-tag:before {
  content: "\e755";
}
.submail-v2-tags:before {
  content: "\e756";
}
.submail-v2-receipt:before {
  content: "\e757";
}
.submail-v2-wallet:before {
  content: "\e758";
}
.submail-v2-credit-card:before {
  content: "\e759";
}
.submail-v2-cash-dollar:before {
  content: "\e75a";
}
.submail-v2-cash-euro:before {
  content: "\e75b";
}
.submail-v2-cash-pound:before {
  content: "\e75c";
}
.submail-v2-cash-yen:before {
  content: "\e75d";
}
.submail-v2-bag-dollar:before {
  content: "\e75e";
}
.submail-v2-bag-euro:before {
  content: "\e75f";
}
.submail-v2-bag-pound:before {
  content: "\e760";
}
.submail-v2-bag-yen:before {
  content: "\e761";
}
.submail-v2-coin-dollar:before {
  content: "\e762";
}
.submail-v2-coin-euro:before {
  content: "\e763";
}
.submail-v2-coin-pound:before {
  content: "\e764";
}
.submail-v2-coin-yen:before {
  content: "\e765";
}
.submail-v2-calculator:before {
  content: "\e766";
}
.submail-v2-calculator2:before {
  content: "\e767";
}
.submail-v2-abacus:before {
  content: "\e768";
}
.submail-v2-vault:before {
  content: "\e769";
}
.submail-v2-telephone:before {
  content: "\e76a";
}
.submail-v2-phone-lock:before {
  content: "\e76b";
}
.submail-v2-phone-wave:before {
  content: "\e76c";
}
.submail-v2-phone-pause:before {
  content: "\e76d";
}
.submail-v2-phone-outgoing:before {
  content: "\e76e";
}
.submail-v2-phone-incoming:before {
  content: "\e76f";
}
.submail-v2-phone-in-out:before {
  content: "\e770";
}
.submail-v2-phone-error:before {
  content: "\e771";
}
.submail-v2-phone-sip:before {
  content: "\e772";
}
.submail-v2-phone-plus:before {
  content: "\e773";
}
.submail-v2-phone-minus:before {
  content: "\e774";
}
.submail-v2-voicemail:before {
  content: "\e775";
}
.submail-v2-dial:before {
  content: "\e776";
}
.submail-v2-telephone2:before {
  content: "\e777";
}
.submail-v2-pushpin:before {
  content: "\e778";
}
.submail-v2-pushpin2:before {
  content: "\e779";
}
.submail-v2-map-marker:before {
  content: "\e77a";
}
.submail-v2-map-marker-user:before {
  content: "\e77b";
}
.submail-v2-map-marker-down:before {
  content: "\e77c";
}
.submail-v2-map-marker-check:before {
  content: "\e77d";
}
.submail-v2-map-marker-crossed:before {
  content: "\e77e";
}
.submail-v2-radar:before {
  content: "\e77f";
}
.submail-v2-compass2:before {
  content: "\e780";
}
.submail-v2-map:before {
  content: "\e781";
}
.submail-v2-map2:before {
  content: "\e782";
}
.submail-v2-location:before {
  content: "\e783";
}
.submail-v2-road-sign:before {
  content: "\e784";
}
.submail-v2-calendar-empty:before {
  content: "\e785";
}
.submail-v2-calendar-check:before {
  content: "\e786";
}
.submail-v2-calendar-cross:before {
  content: "\e787";
}
.submail-v2-calendar-31:before {
  content: "\e788";
}
.submail-v2-calendar-full:before {
  content: "\e789";
}
.submail-v2-calendar-insert:before {
  content: "\e78a";
}
.submail-v2-calendar-text:before {
  content: "\e78b";
}
.submail-v2-calendar-user:before {
  content: "\e78c";
}
.submail-v2-mouse:before {
  content: "\e78d";
}
.submail-v2-mouse-left:before {
  content: "\e78e";
}
.submail-v2-mouse-right:before {
  content: "\e78f";
}
.submail-v2-mouse-both:before {
  content: "\e790";
}
.submail-v2-keyboard:before {
  content: "\e791";
}
.submail-v2-keyboard-up:before {
  content: "\e792";
}
.submail-v2-keyboard-down:before {
  content: "\e793";
}
.submail-v2-delete:before {
  content: "\e794";
}
.submail-v2-spell-check:before {
  content: "\e795";
}
.submail-v2-escape:before {
  content: "\e796";
}
.submail-v2-enter2:before {
  content: "\e797";
}
.submail-v2-screen:before {
  content: "\e798";
}
.submail-v2-aspect-ratio:before {
  content: "\e799";
}
.submail-v2-signal:before {
  content: "\e79a";
}
.submail-v2-signal-lock:before {
  content: "\e79b";
}
.submail-v2-signal-80:before {
  content: "\e79c";
}
.submail-v2-signal-60:before {
  content: "\e79d";
}
.submail-v2-signal-40:before {
  content: "\e79e";
}
.submail-v2-signal-20:before {
  content: "\e79f";
}
.submail-v2-signal-0:before {
  content: "\e7a0";
}
.submail-v2-signal-blocked:before {
  content: "\e7a1";
}
.submail-v2-sim:before {
  content: "\e7a2";
}
.submail-v2-flash-memory:before {
  content: "\e7a3";
}
.submail-v2-usb-drive:before {
  content: "\e7a4";
}
.submail-v2-phone:before {
  content: "\e7a5";
}
.submail-v2-smartphone:before {
  content: "\e7a6";
}
.submail-v2-smartphone-notification:before {
  content: "\e7a7";
}
.submail-v2-smartphone-vibration:before {
  content: "\e7a8";
}
.submail-v2-smartphone-embed:before {
  content: "\e7a9";
}
.submail-v2-smartphone-waves:before {
  content: "\e7aa";
}
.submail-v2-tablet:before {
  content: "\e7ab";
}
.submail-v2-tablet2:before {
  content: "\e7ac";
}
.submail-v2-laptop:before {
  content: "\e7ad";
}
.submail-v2-laptop-phone:before {
  content: "\e7ae";
}
.submail-v2-desktop:before {
  content: "\e7af";
}
.submail-v2-launch:before {
  content: "\e7b0";
}
.submail-v2-new-tab:before {
  content: "\e7b1";
}
.submail-v2-window:before {
  content: "\e7b2";
}
.submail-v2-cable:before {
  content: "\e7b3";
}
.submail-v2-cable2:before {
  content: "\e7b4";
}
.submail-v2-tv:before {
  content: "\e7b5";
}
.submail-v2-radio:before {
  content: "\e7b6";
}
.submail-v2-remote-control:before {
  content: "\e7b7";
}
.submail-v2-power-switch:before {
  content: "\e7b8";
}
.submail-v2-power:before {
  content: "\e7b9";
}
.submail-v2-power-crossed:before {
  content: "\e7ba";
}
.submail-v2-flash-auto:before {
  content: "\e7bb";
}
.submail-v2-lamp:before {
  content: "\e7bc";
}
.submail-v2-flashlight:before {
  content: "\e7bd";
}
.submail-v2-lampshade:before {
  content: "\e7be";
}
.submail-v2-cord:before {
  content: "\e7bf";
}
.submail-v2-outlet:before {
  content: "\e7c0";
}
.submail-v2-battery-power:before {
  content: "\e7c1";
}
.submail-v2-battery-empty:before {
  content: "\e7c2";
}
.submail-v2-battery-alert:before {
  content: "\e7c3";
}
.submail-v2-battery-error:before {
  content: "\e7c4";
}
.submail-v2-battery-low1:before {
  content: "\e7c5";
}
.submail-v2-battery-low2:before {
  content: "\e7c6";
}
.submail-v2-battery-low3:before {
  content: "\e7c7";
}
.submail-v2-battery-mid1:before {
  content: "\e7c8";
}
.submail-v2-battery-mid2:before {
  content: "\e7c9";
}
.submail-v2-battery-mid3:before {
  content: "\e7ca";
}
.submail-v2-battery-full:before {
  content: "\e7cb";
}
.submail-v2-battery-charging:before {
  content: "\e7cc";
}
.submail-v2-battery-charging2:before {
  content: "\e7cd";
}
.submail-v2-battery-charging3:before {
  content: "\e7ce";
}
.submail-v2-battery-charging4:before {
  content: "\e7cf";
}
.submail-v2-battery-charging5:before {
  content: "\e7d0";
}
.submail-v2-battery-charging6:before {
  content: "\e7d1";
}
.submail-v2-battery-charging7:before {
  content: "\e7d2";
}
.submail-v2-chip:before {
  content: "\e7d3";
}
.submail-v2-chip-x64:before {
  content: "\e7d4";
}
.submail-v2-chip-x86:before {
  content: "\e7d5";
}
.submail-v2-bubble:before {
  content: "\e7d6";
}
.submail-v2-bubbles:before {
  content: "\e7d7";
}
.submail-v2-bubble-dots:before {
  content: "\e7d8";
}
.submail-v2-bubble-alert:before {
  content: "\e7d9";
}
.submail-v2-bubble-question:before {
  content: "\e7da";
}
.submail-v2-bubble-text:before {
  content: "\e7db";
}
.submail-v2-bubble-pencil:before {
  content: "\e7dc";
}
.submail-v2-bubble-picture:before {
  content: "\e7dd";
}
.submail-v2-bubble-video:before {
  content: "\e7de";
}
.submail-v2-bubble-user:before {
  content: "\e7df";
}
.submail-v2-bubble-quote:before {
  content: "\e7e0";
}
.submail-v2-bubble-heart:before {
  content: "\e7e1";
}
.submail-v2-bubble-emoticon:before {
  content: "\e7e2";
}
.submail-v2-bubble-attachment:before {
  content: "\e7e3";
}
.submail-v2-phone-bubble:before {
  content: "\e7e4";
}
.submail-v2-quote-open:before {
  content: "\e7e5";
}
.submail-v2-quote-close:before {
  content: "\e7e6";
}
.submail-v2-dna:before {
  content: "\e7e7";
}
.submail-v2-heart-pulse:before {
  content: "\e7e8";
}
.submail-v2-pulse:before {
  content: "\e7e9";
}
.submail-v2-syringe:before {
  content: "\e7ea";
}
.submail-v2-pills:before {
  content: "\e7eb";
}
.submail-v2-first-aid:before {
  content: "\e7ec";
}
.submail-v2-lifebuoy:before {
  content: "\e7ed";
}
.submail-v2-bandage:before {
  content: "\e7ee";
}
.submail-v2-bandages:before {
  content: "\e7ef";
}
.submail-v2-thermometer:before {
  content: "\e7f0";
}
.submail-v2-microscope:before {
  content: "\e7f1";
}
.submail-v2-brain:before {
  content: "\e7f2";
}
.submail-v2-beaker:before {
  content: "\e7f3";
}
.submail-v2-skull:before {
  content: "\e7f4";
}
.submail-v2-bone:before {
  content: "\e7f5";
}
.submail-v2-construction:before {
  content: "\e7f6";
}
.submail-v2-construction-cone:before {
  content: "\e7f7";
}
.submail-v2-pie-chart:before {
  content: "\e7f8";
}
.submail-v2-pie-chart2:before {
  content: "\e7f9";
}
.submail-v2-graph:before {
  content: "\e7fa";
}
.submail-v2-chart-growth:before {
  content: "\e7fb";
}
.submail-v2-chart-bars:before {
  content: "\e7fc";
}
.submail-v2-chart-settings:before {
  content: "\e7fd";
}
.submail-v2-cake:before {
  content: "\e7fe";
}
.submail-v2-gift:before {
  content: "\e7ff";
}
.submail-v2-balloon:before {
  content: "\e800";
}
.submail-v2-rank:before {
  content: "\e801";
}
.submail-v2-rank2:before {
  content: "\e802";
}
.submail-v2-rank3:before {
  content: "\e803";
}
.submail-v2-crown:before {
  content: "\e804";
}
.submail-v2-lotus:before {
  content: "\e805";
}
.submail-v2-diamond:before {
  content: "\e806";
}
.submail-v2-diamond2:before {
  content: "\e807";
}
.submail-v2-diamond3:before {
  content: "\e808";
}
.submail-v2-diamond4:before {
  content: "\e809";
}
.submail-v2-linearicons:before {
  content: "\e80a";
}
.submail-v2-teacup:before {
  content: "\e80b";
}
.submail-v2-teapot:before {
  content: "\e80c";
}
.submail-v2-glass:before {
  content: "\e80d";
}
.submail-v2-bottle2:before {
  content: "\e80e";
}
.submail-v2-glass-cocktail:before {
  content: "\e80f";
}
.submail-v2-glass2:before {
  content: "\e810";
}
.submail-v2-dinner:before {
  content: "\e811";
}
.submail-v2-dinner2:before {
  content: "\e812";
}
.submail-v2-chef:before {
  content: "\e813";
}
.submail-v2-scale2:before {
  content: "\e814";
}
.submail-v2-egg:before {
  content: "\e815";
}
.submail-v2-egg2:before {
  content: "\e816";
}
.submail-v2-eggs:before {
  content: "\e817";
}
.submail-v2-platter:before {
  content: "\e818";
}
.submail-v2-steak:before {
  content: "\e819";
}
.submail-v2-hamburger:before {
  content: "\e81a";
}
.submail-v2-hotdog:before {
  content: "\e81b";
}
.submail-v2-pizza:before {
  content: "\e81c";
}
.submail-v2-sausage:before {
  content: "\e81d";
}
.submail-v2-chicken:before {
  content: "\e81e";
}
.submail-v2-fish:before {
  content: "\e81f";
}
.submail-v2-carrot:before {
  content: "\e820";
}
.submail-v2-cheese:before {
  content: "\e821";
}
.submail-v2-bread:before {
  content: "\e822";
}
.submail-v2-ice-cream:before {
  content: "\e823";
}
.submail-v2-ice-cream2:before {
  content: "\e824";
}
.submail-v2-candy:before {
  content: "\e825";
}
.submail-v2-lollipop:before {
  content: "\e826";
}
.submail-v2-coffee-bean:before {
  content: "\e827";
}
.submail-v2-coffee-cup:before {
  content: "\e828";
}
.submail-v2-cherry:before {
  content: "\e829";
}
.submail-v2-grapes:before {
  content: "\e82a";
}
.submail-v2-citrus:before {
  content: "\e82b";
}
.submail-v2-apple:before {
  content: "\e82c";
}
.submail-v2-leaf:before {
  content: "\e82d";
}
.submail-v2-landscape:before {
  content: "\e82e";
}
.submail-v2-pine-tree:before {
  content: "\e82f";
}
.submail-v2-tree:before {
  content: "\e830";
}
.submail-v2-cactus:before {
  content: "\e831";
}
.submail-v2-paw:before {
  content: "\e832";
}
.submail-v2-footprint:before {
  content: "\e833";
}
.submail-v2-speed-slow:before {
  content: "\e834";
}
.submail-v2-speed-medium:before {
  content: "\e835";
}
.submail-v2-speed-fast:before {
  content: "\e836";
}
.submail-v2-rocket:before {
  content: "\e837";
}
.submail-v2-hammer2:before {
  content: "\e838";
}
.submail-v2-balance:before {
  content: "\e839";
}
.submail-v2-briefcase:before {
  content: "\e83a";
}
.submail-v2-luggage-weight:before {
  content: "\e83b";
}
.submail-v2-dolly:before {
  content: "\e83c";
}
.submail-v2-plane:before {
  content: "\e83d";
}
.submail-v2-plane-crossed:before {
  content: "\e83e";
}
.submail-v2-helicopter:before {
  content: "\e83f";
}
.submail-v2-traffic-lights:before {
  content: "\e840";
}
.submail-v2-siren:before {
  content: "\e841";
}
.submail-v2-road:before {
  content: "\e842";
}
.submail-v2-engine:before {
  content: "\e843";
}
.submail-v2-oil-pressure:before {
  content: "\e844";
}
.submail-v2-coolant-temperature:before {
  content: "\e845";
}
.submail-v2-car-battery:before {
  content: "\e846";
}
.submail-v2-gas:before {
  content: "\e847";
}
.submail-v2-gallon:before {
  content: "\e848";
}
.submail-v2-transmission:before {
  content: "\e849";
}
.submail-v2-car:before {
  content: "\e84a";
}
.submail-v2-car-wash:before {
  content: "\e84b";
}
.submail-v2-car-wash2:before {
  content: "\e84c";
}
.submail-v2-bus:before {
  content: "\e84d";
}
.submail-v2-bus2:before {
  content: "\e84e";
}
.submail-v2-car2:before {
  content: "\e84f";
}
.submail-v2-parking:before {
  content: "\e850";
}
.submail-v2-car-lock:before {
  content: "\e851";
}
.submail-v2-taxi:before {
  content: "\e852";
}
.submail-v2-car-siren:before {
  content: "\e853";
}
.submail-v2-car-wash3:before {
  content: "\e854";
}
.submail-v2-car-wash4:before {
  content: "\e855";
}
.submail-v2-ambulance:before {
  content: "\e856";
}
.submail-v2-truck:before {
  content: "\e857";
}
.submail-v2-trailer:before {
  content: "\e858";
}
.submail-v2-scale-truck:before {
  content: "\e859";
}
.submail-v2-train:before {
  content: "\e85a";
}
.submail-v2-ship:before {
  content: "\e85b";
}
.submail-v2-ship2:before {
  content: "\e85c";
}
.submail-v2-anchor:before {
  content: "\e85d";
}
.submail-v2-boat:before {
  content: "\e85e";
}
.submail-v2-bicycle:before {
  content: "\e85f";
}
.submail-v2-bicycle2:before {
  content: "\e860";
}
.submail-v2-dumbbell:before {
  content: "\e861";
}
.submail-v2-bench-press:before {
  content: "\e862";
}
.submail-v2-swim:before {
  content: "\e863";
}
.submail-v2-football:before {
  content: "\e864";
}
.submail-v2-baseball-bat:before {
  content: "\e865";
}
.submail-v2-baseball:before {
  content: "\e866";
}
.submail-v2-tennis:before {
  content: "\e867";
}
.submail-v2-tennis2:before {
  content: "\e868";
}
.submail-v2-ping-pong:before {
  content: "\e869";
}
.submail-v2-hockey:before {
  content: "\e86a";
}
.submail-v2-8ball:before {
  content: "\e86b";
}
.submail-v2-bowling:before {
  content: "\e86c";
}
.submail-v2-bowling-pins:before {
  content: "\e86d";
}
.submail-v2-golf:before {
  content: "\e86e";
}
.submail-v2-golf2:before {
  content: "\e86f";
}
.submail-v2-archery:before {
  content: "\e870";
}
.submail-v2-slingshot:before {
  content: "\e871";
}
.submail-v2-soccer:before {
  content: "\e872";
}
.submail-v2-basketball:before {
  content: "\e873";
}
.submail-v2-cube:before {
  content: "\e874";
}
.submail-v2-3d-rotate:before {
  content: "\e875";
}
.submail-v2-puzzle:before {
  content: "\e876";
}
.submail-v2-glasses:before {
  content: "\e877";
}
.submail-v2-glasses2:before {
  content: "\e878";
}
.submail-v2-accessibility:before {
  content: "\e879";
}
.submail-v2-wheelchair:before {
  content: "\e87a";
}
.submail-v2-wall:before {
  content: "\e87b";
}
.submail-v2-fence:before {
  content: "\e87c";
}
.submail-v2-wall2:before {
  content: "\e87d";
}
.submail-v2-icons:before {
  content: "\e87e";
}
.submail-v2-resize-handle:before {
  content: "\e87f";
}
.submail-v2-icons2:before {
  content: "\e880";
}
.submail-v2-select:before {
  content: "\e881";
}
.submail-v2-select2:before {
  content: "\e882";
}
.submail-v2-site-map:before {
  content: "\e883";
}
.submail-v2-earth:before {
  content: "\e884";
}
.submail-v2-earth-lock:before {
  content: "\e885";
}
.submail-v2-network:before {
  content: "\e886";
}
.submail-v2-network-lock:before {
  content: "\e887";
}
.submail-v2-planet:before {
  content: "\e888";
}
.submail-v2-happy:before {
  content: "\e889";
}
.submail-v2-smile:before {
  content: "\e88a";
}
.submail-v2-grin:before {
  content: "\e88b";
}
.submail-v2-tongue:before {
  content: "\e88c";
}
.submail-v2-sad:before {
  content: "\e88d";
}
.submail-v2-wink:before {
  content: "\e88e";
}
.submail-v2-dream:before {
  content: "\e88f";
}
.submail-v2-shocked:before {
  content: "\e890";
}
.submail-v2-shocked2:before {
  content: "\e891";
}
.submail-v2-tongue2:before {
  content: "\e892";
}
.submail-v2-neutral:before {
  content: "\e893";
}
.submail-v2-happy-grin:before {
  content: "\e894";
}
.submail-v2-cool:before {
  content: "\e895";
}
.submail-v2-mad:before {
  content: "\e896";
}
.submail-v2-grin-evil:before {
  content: "\e897";
}
.submail-v2-evil:before {
  content: "\e898";
}
.submail-v2-wow:before {
  content: "\e899";
}
.submail-v2-annoyed:before {
  content: "\e89a";
}
.submail-v2-wondering:before {
  content: "\e89b";
}
.submail-v2-confused:before {
  content: "\e89c";
}
.submail-v2-zipped:before {
  content: "\e89d";
}
.submail-v2-grumpy:before {
  content: "\e89e";
}
.submail-v2-mustache:before {
  content: "\e89f";
}
.submail-v2-tombstone-hipster:before {
  content: "\e8a0";
}
.submail-v2-tombstone:before {
  content: "\e8a1";
}
.submail-v2-ghost:before {
  content: "\e8a2";
}
.submail-v2-ghost-hipster:before {
  content: "\e8a3";
}
.submail-v2-halloween:before {
  content: "\e8a4";
}
.submail-v2-christmas:before {
  content: "\e8a5";
}
.submail-v2-easter-egg:before {
  content: "\e8a6";
}
.submail-v2-mustache2:before {
  content: "\e8a7";
}
.submail-v2-mustache-glasses:before {
  content: "\e8a8";
}
.submail-v2-pipe:before {
  content: "\e8a9";
}
.submail-v2-alarm:before {
  content: "\e8aa";
}
.submail-v2-alarm-add:before {
  content: "\e8ab";
}
.submail-v2-alarm-snooze:before {
  content: "\e8ac";
}
.submail-v2-alarm-ringing:before {
  content: "\e8ad";
}
.submail-v2-bullhorn:before {
  content: "\e8ae";
}
.submail-v2-hearing:before {
  content: "\e8af";
}
.submail-v2-volume-high:before {
  content: "\e8b0";
}
.submail-v2-volume-medium:before {
  content: "\e8b1";
}
.submail-v2-volume-low:before {
  content: "\e8b2";
}
.submail-v2-volume:before {
  content: "\e8b3";
}
.submail-v2-mute:before {
  content: "\e8b4";
}
.submail-v2-lan:before {
  content: "\e8b5";
}
.submail-v2-lan2:before {
  content: "\e8b6";
}
.submail-v2-wifi:before {
  content: "\e8b7";
}
.submail-v2-wifi-lock:before {
  content: "\e8b8";
}
.submail-v2-wifi-blocked:before {
  content: "\e8b9";
}
.submail-v2-wifi-mid:before {
  content: "\e8ba";
}
.submail-v2-wifi-low:before {
  content: "\e8bb";
}
.submail-v2-wifi-low2:before {
  content: "\e8bc";
}
.submail-v2-wifi-alert:before {
  content: "\e8bd";
}
.submail-v2-wifi-alert-mid:before {
  content: "\e8be";
}
.submail-v2-wifi-alert-low:before {
  content: "\e8bf";
}
.submail-v2-wifi-alert-low2:before {
  content: "\e8c0";
}
.submail-v2-stream:before {
  content: "\e8c1";
}
.submail-v2-stream-check:before {
  content: "\e8c2";
}
.submail-v2-stream-error:before {
  content: "\e8c3";
}
.submail-v2-stream-alert:before {
  content: "\e8c4";
}
.submail-v2-communication:before {
  content: "\e8c5";
}
.submail-v2-communication-crossed:before {
  content: "\e8c6";
}
.submail-v2-broadcast:before {
  content: "\e8c7";
}
.submail-v2-antenna:before {
  content: "\e8c8";
}
.submail-v2-satellite:before {
  content: "\e8c9";
}
.submail-v2-satellite2:before {
  content: "\e8ca";
}
.submail-v2-mic:before {
  content: "\e8cb";
}
.submail-v2-mic-mute:before {
  content: "\e8cc";
}
.submail-v2-mic2:before {
  content: "\e8cd";
}
.submail-v2-spotlights:before {
  content: "\e8ce";
}
.submail-v2-hourglass:before {
  content: "\e8cf";
}
.submail-v2-loading:before {
  content: "\e8d0";
}
.submail-v2-loading2:before {
  content: "\e8d1";
}
.submail-v2-loading3:before {
  content: "\e8d2";
}
.submail-v2-refresh:before {
  content: "\e8d3";
}
.submail-v2-refresh2:before {
  content: "\e8d4";
}
.submail-v2-undo:before {
  content: "\e8d5";
}
.submail-v2-redo:before {
  content: "\e8d6";
}
.submail-v2-jump2:before {
  content: "\e8d7";
}
.submail-v2-undo2:before {
  content: "\e8d8";
}
.submail-v2-redo2:before {
  content: "\e8d9";
}
.submail-v2-sync:before {
  content: "\e8da";
}
.submail-v2-repeat-one2:before {
  content: "\e8db";
}
.submail-v2-sync-crossed:before {
  content: "\e8dc";
}
.submail-v2-sync2:before {
  content: "\e8dd";
}
.submail-v2-repeat-one3:before {
  content: "\e8de";
}
.submail-v2-sync-crossed2:before {
  content: "\e8df";
}
.submail-v2-return:before {
  content: "\e8e0";
}
.submail-v2-return2:before {
  content: "\e8e1";
}
.submail-v2-refund:before {
  content: "\e8e2";
}
.submail-v2-history:before {
  content: "\e8e3";
}
.submail-v2-history2:before {
  content: "\e8e4";
}
.submail-v2-self-timer:before {
  content: "\e8e5";
}
.submail-v2-clock:before {
  content: "\e8e6";
}
.submail-v2-clock2:before {
  content: "\e8e7";
}
.submail-v2-clock3:before {
  content: "\e8e8";
}
.submail-v2-watch:before {
  content: "\e8e9";
}
.submail-v2-alarm2:before {
  content: "\e8ea";
}
.submail-v2-alarm-add2:before {
  content: "\e8eb";
}
.submail-v2-alarm-remove:before {
  content: "\e8ec";
}
.submail-v2-alarm-check:before {
  content: "\e8ed";
}
.submail-v2-alarm-error:before {
  content: "\e8ee";
}
.submail-v2-timer:before {
  content: "\e8ef";
}
.submail-v2-timer-crossed:before {
  content: "\e8f0";
}
.submail-v2-timer2:before {
  content: "\e8f1";
}
.submail-v2-timer-crossed2:before {
  content: "\e8f2";
}
.submail-v2-download:before {
  content: "\e8f3";
}
.submail-v2-upload:before {
  content: "\e8f4";
}
.submail-v2-download2:before {
  content: "\e8f5";
}
.submail-v2-upload2:before {
  content: "\e8f6";
}
.submail-v2-enter-up:before {
  content: "\e8f7";
}
.submail-v2-enter-down:before {
  content: "\e8f8";
}
.submail-v2-enter-left:before {
  content: "\e8f9";
}
.submail-v2-enter-right:before {
  content: "\e8fa";
}
.submail-v2-exit-up:before {
  content: "\e8fb";
}
.submail-v2-exit-down:before {
  content: "\e8fc";
}
.submail-v2-exit-left:before {
  content: "\e8fd";
}
.submail-v2-exit-right:before {
  content: "\e8fe";
}
.submail-v2-enter-up2:before {
  content: "\e8ff";
}
.submail-v2-enter-down2:before {
  content: "\e900";
}
.submail-v2-enter-vertical:before {
  content: "\e901";
}
.submail-v2-enter-left2:before {
  content: "\e902";
}
.submail-v2-enter-right2:before {
  content: "\e903";
}
.submail-v2-enter-horizontal:before {
  content: "\e904";
}
.submail-v2-exit-up2:before {
  content: "\e905";
}
.submail-v2-exit-down2:before {
  content: "\e906";
}
.submail-v2-exit-left2:before {
  content: "\e907";
}
.submail-v2-exit-right2:before {
  content: "\e908";
}
.submail-v2-cli:before {
  content: "\e909";
}
.submail-v2-bug:before {
  content: "\e90a";
}
.submail-v2-code:before {
  content: "\e90b";
}
.submail-v2-file-code:before {
  content: "\e90c";
}
.submail-v2-file-image:before {
  content: "\e90d";
}
.submail-v2-file-zip:before {
  content: "\e90e";
}
.submail-v2-file-audio:before {
  content: "\e90f";
}
.submail-v2-file-video:before {
  content: "\e910";
}
.submail-v2-file-preview:before {
  content: "\e911";
}
.submail-v2-file-charts:before {
  content: "\e912";
}
.submail-v2-file-stats:before {
  content: "\e913";
}
.submail-v2-file-spreadsheet:before {
  content: "\e914";
}
.submail-v2-link:before {
  content: "\e915";
}
.submail-v2-unlink:before {
  content: "\e916";
}
.submail-v2-link2:before {
  content: "\e917";
}
.submail-v2-unlink2:before {
  content: "\e918";
}
.submail-v2-thumbs-up:before {
  content: "\e919";
}
.submail-v2-thumbs-down:before {
  content: "\e91a";
}
.submail-v2-thumbs-up2:before {
  content: "\e91d";
}
.submail-v2-thumbs-down2:before {
  content: "\e91e";
}
.submail-v2-thumbs-up3:before {
  content: "\e91f";
}
.submail-v2-thumbs-down3:before {
  content: "\e920";
}
.submail-v2-share:before {
  content: "\e922";
}
.submail-v2-share2:before {
  content: "\e923";
}
.submail-v2-share3:before {
  content: "\e925";
}
.submail-v2-magnifier:before {
  content: "\e926";
}
.submail-v2-file-search:before {
  content: "\e927";
}
.submail-v2-find-replace:before {
  content: "\e928";
}
.submail-v2-zoom-in:before {
  content: "\e929";
}
.submail-v2-zoom-out:before {
  content: "\e92a";
}
.submail-v2-loupe:before {
  content: "\e92b";
}
.submail-v2-loupe-zoom-in:before {
  content: "\e92c";
}
.submail-v2-loupe-zoom-out:before {
  content: "\e92d";
}
.submail-v2-cross:before {
  content: "\e92e";
}
.submail-v2-menu:before {
  content: "\e92f";
}
.submail-v2-list:before {
  content: "\e930";
}
.submail-v2-list2:before {
  content: "\e932";
}
.submail-v2-list3:before {
  content: "\e933";
}
.submail-v2-menu2:before {
  content: "\e934";
}
.submail-v2-list4:before {
  content: "\e935";
}
.submail-v2-menu3:before {
  content: "\e936";
}
.submail-v2-exclamation:before {
  content: "\e937";
}
.submail-v2-question:before {
  content: "\e938";
}
.submail-v2-check:before {
  content: "\e939";
}
.submail-v2-cross2:before {
  content: "\e93a";
}
.submail-v2-plus:before {
  content: "\e93b";
}
.submail-v2-minus:before {
  content: "\e93c";
}
.submail-v2-percent:before {
  content: "\e93d";
}
.submail-v2-chevron-up:before {
  content: "\e93e";
}
.submail-v2-chevron-down:before {
  content: "\e93f";
}
.submail-v2-chevron-left:before {
  content: "\e940";
}
.submail-v2-chevron-right:before {
  content: "\e941";
}
.submail-v2-chevrons-expand-vertical:before {
  content: "\e942";
}
.submail-v2-chevrons-expand-horizontal:before {
  content: "\e943";
}
.submail-v2-chevrons-contract-vertical:before {
  content: "\e944";
}
.submail-v2-chevrons-contract-horizontal:before {
  content: "\e945";
}
.submail-v2-arrow-up:before {
  content: "\e946";
}
.submail-v2-arrow-down:before {
  content: "\e947";
}
.submail-v2-arrow-left:before {
  content: "\e948";
}
.submail-v2-arrow-right:before {
  content: "\e949";
}
.submail-v2-arrow-up-right:before {
  content: "\e94a";
}
.submail-v2-arrows-merge:before {
  content: "\e94b";
}
.submail-v2-arrows-split:before {
  content: "\e94c";
}
.submail-v2-arrow-divert:before {
  content: "\e94d";
}
.submail-v2-arrow-return:before {
  content: "\e94e";
}
.submail-v2-expand:before {
  content: "\e94f";
}
.submail-v2-contract:before {
  content: "\e950";
}
.submail-v2-expand2:before {
  content: "\e951";
}
.submail-v2-contract2:before {
  content: "\e952";
}
.submail-v2-move:before {
  content: "\e953";
}
.submail-v2-tab:before {
  content: "\e954";
}
.submail-v2-arrow-wave:before {
  content: "\e955";
}
.submail-v2-expand3:before {
  content: "\e956";
}
.submail-v2-expand4:before {
  content: "\e957";
}
.submail-v2-contract3:before {
  content: "\e958";
}
.submail-v2-notification:before {
  content: "\e959";
}
.submail-v2-warning:before {
  content: "\e95a";
}
.submail-v2-notification-circle:before {
  content: "\e95b";
}
.submail-v2-question-circle:before {
  content: "\e95c";
}
.submail-v2-menu-circle:before {
  content: "\e95d";
}
.submail-v2-checkmark-circle:before {
  content: "\e95e";
}
.submail-v2-cross-circle:before {
  content: "\e95f";
}
.submail-v2-plus-circle:before {
  content: "\e960";
}
.submail-v2-circle-minus:before {
  content: "\e961";
}
.submail-v2-percent-circle:before {
  content: "\e962";
}
.submail-v2-arrow-up-circle:before {
  content: "\e963";
}
.submail-v2-arrow-down-circle:before {
  content: "\e964";
}
.submail-v2-arrow-left-circle:before {
  content: "\e965";
}
.submail-v2-arrow-right-circle:before {
  content: "\e969";
}
.submail-v2-chevron-up-circle:before {
  content: "\e96a";
}
.submail-v2-chevron-down-circle:before {
  content: "\e96b";
}
.submail-v2-chevron-left-circle:before {
  content: "\e96c";
}
.submail-v2-chevron-right-circle:before {
  content: "\e96f";
}
.submail-v2-backward-circle:before {
  content: "\e970";
}
.submail-v2-first-circle:before {
  content: "\e971";
}
.submail-v2-previous-circle:before {
  content: "\e972";
}
.submail-v2-stop-circle:before {
  content: "\e973";
}
.submail-v2-play-circle:before {
  content: "\e974";
}
.submail-v2-pause-circle:before {
  content: "\e975";
}
.submail-v2-next-circle:before {
  content: "\e976";
}
.submail-v2-last-circle:before {
  content: "\e977";
}
.submail-v2-forward-circle:before {
  content: "\e978";
}
.submail-v2-eject-circle:before {
  content: "\e979";
}
.submail-v2-crop:before {
  content: "\e97a";
}
.submail-v2-frame-expand:before {
  content: "\e97b";
}
.submail-v2-frame-contract:before {
  content: "\e97c";
}
.submail-v2-focus:before {
  content: "\e97d";
}
.submail-v2-transform:before {
  content: "\e97e";
}
.submail-v2-grid:before {
  content: "\e97f";
}
.submail-v2-grid-crossed:before {
  content: "\e980";
}
.submail-v2-layers:before {
  content: "\e981";
}
.submail-v2-layers-crossed:before {
  content: "\e982";
}
.submail-v2-toggle:before {
  content: "\e983";
}
.submail-v2-rulers:before {
  content: "\e984";
}
.submail-v2-ruler:before {
  content: "\e985";
}
.submail-v2-funnel:before {
  content: "\e986";
}
.submail-v2-flip-horizontal:before {
  content: "\e987";
}
.submail-v2-flip-vertical:before {
  content: "\e988";
}
.submail-v2-flip-horizontal2:before {
  content: "\e989";
}
.submail-v2-flip-vertical2:before {
  content: "\e98a";
}
.submail-v2-angle:before {
  content: "\e98b";
}
.submail-v2-angle2:before {
  content: "\e98c";
}
.submail-v2-subtract:before {
  content: "\e98d";
}
.submail-v2-combine:before {
  content: "\e98e";
}
.submail-v2-intersect:before {
  content: "\e98f";
}
.submail-v2-exclude:before {
  content: "\e990";
}
.submail-v2-align-center-vertical:before {
  content: "\e991";
}
.submail-v2-align-right:before {
  content: "\e992";
}
.submail-v2-align-bottom:before {
  content: "\e993";
}
.submail-v2-align-left:before {
  content: "\e994";
}
.submail-v2-align-center-horizontal:before {
  content: "\e995";
}
.submail-v2-align-top:before {
  content: "\e996";
}
.submail-v2-square:before {
  content: "\e997";
}
.submail-v2-plus-square:before {
  content: "\e998";
}
.submail-v2-minus-square:before {
  content: "\e999";
}
.submail-v2-percent-square:before {
  content: "\e99a";
}
.submail-v2-arrow-up-square:before {
  content: "\e99b";
}
.submail-v2-arrow-down-square:before {
  content: "\e99c";
}
.submail-v2-arrow-left-square:before {
  content: "\e99d";
}
.submail-v2-arrow-right-square:before {
  content: "\e99e";
}
.submail-v2-chevron-up-square:before {
  content: "\e99f";
}
.submail-v2-chevron-down-square:before {
  content: "\e9a0";
}
.submail-v2-chevron-left-square:before {
  content: "\e9a1";
}
.submail-v2-chevron-right-square:before {
  content: "\e9a2";
}
.submail-v2-check-square:before {
  content: "\e9a3";
}
.submail-v2-cross-square:before {
  content: "\e9a4";
}
.submail-v2-menu-square:before {
  content: "\e9a5";
}
.submail-v2-prohibited:before {
  content: "\e9a6";
}
.submail-v2-circle:before {
  content: "\e9a7";
}
.submail-v2-radio-button:before {
  content: "\e9a8";
}
.submail-v2-ligature:before {
  content: "\e9a9";
}
.submail-v2-text-format:before {
  content: "\e9aa";
}
.submail-v2-text-format-remove:before {
  content: "\e9ab";
}
.submail-v2-text-size:before {
  content: "\e9ac";
}
.submail-v2-bold:before {
  content: "\e9ad";
}
.submail-v2-italic:before {
  content: "\e9ae";
}
.submail-v2-underline:before {
  content: "\e9af";
}
.submail-v2-strikethrough:before {
  content: "\e9b0";
}
.submail-v2-highlight:before {
  content: "\e9b1";
}
.submail-v2-text-align-left:before {
  content: "\e9b2";
}
.submail-v2-text-align-center:before {
  content: "\e9b3";
}
.submail-v2-text-align-right:before {
  content: "\e9b4";
}
.submail-v2-text-align-justify:before {
  content: "\e9b5";
}
.submail-v2-line-spacing:before {
  content: "\e9b6";
}
.submail-v2-indent-increase:before {
  content: "\e9b7";
}
.submail-v2-indent-decrease:before {
  content: "\e9b8";
}
.submail-v2-text-wrap:before {
  content: "\e9b9";
}
.submail-v2-pilcrow:before {
  content: "\e9ba";
}
.submail-v2-direction-ltr:before {
  content: "\e9bb";
}
.submail-v2-direction-rtl:before {
  content: "\e9bc";
}
.submail-v2-page-break:before {
  content: "\e9bd";
}
.submail-v2-page-break2:before {
  content: "\e9be";
}
.submail-v2-sort-alpha-asc:before {
  content: "\e9bf";
}
.submail-v2-sort-alpha-desc:before {
  content: "\e9c0";
}
.submail-v2-sort-numeric-asc:before {
  content: "\e9c1";
}
.submail-v2-sort-numeric-desc:before {
  content: "\e9c2";
}
.submail-v2-sort-amount-asc:before {
  content: "\e9c3";
}
.submail-v2-sort-amount-desc:before {
  content: "\e9c4";
}
.submail-v2-sort-time-asc:before {
  content: "\e9c5";
}
.submail-v2-sort-time-desc:before {
  content: "\e9c7";
}
.submail-v2-sigma:before {
  content: "\e9c8";
}
.submail-v2-pencil-line:before {
  content: "\e9cb";
}
.submail-v2-hand:before {
  content: "\e9cc";
}
.submail-v2-pointer-up:before {
  content: "\e9cd";
}
.submail-v2-pointer-right:before {
  content: "\e9ce";
}
.submail-v2-pointer-down:before {
  content: "\e9cf";
}
.submail-v2-pointer-left:before {
  content: "\e9d0";
}
.submail-v2-finger-tap:before {
  content: "\e9d1";
}
.submail-v2-fingers-tap:before {
  content: "\e9d2";
}
.submail-v2-reminder:before {
  content: "\e9d3";
}
.submail-v2-fingers-crossed:before {
  content: "\e9d4";
}
.submail-v2-fingers-victory:before {
  content: "\e9d5";
}
.submail-v2-gesture-zoom:before {
  content: "\e9d6";
}
.submail-v2-gesture-pinch:before {
  content: "\e9d7";
}
.submail-v2-fingers-scroll-horizontal:before {
  content: "\e9d8";
}
.submail-v2-fingers-scroll-vertical:before {
  content: "\e9d9";
}
.submail-v2-fingers-scroll-left:before {
  content: "\e9da";
}
.submail-v2-fingers-scroll-right:before {
  content: "\e9db";
}
.submail-v2-hand2:before {
  content: "\e9dc";
}
.submail-v2-pointer-up2:before {
  content: "\e9dd";
}
.submail-v2-pointer-right2:before {
  content: "\e9de";
}
.submail-v2-pointer-down2:before {
  content: "\e9df";
}
.submail-v2-pointer-left2:before {
  content: "\e9e0";
}
.submail-v2-finger-tap2:before {
  content: "\e9e1";
}
.submail-v2-fingers-tap2:before {
  content: "\e9e2";
}
.submail-v2-reminder2:before {
  content: "\e9e3";
}
.submail-v2-gesture-zoom2:before {
  content: "\e9e4";
}
.submail-v2-gesture-pinch2:before {
  content: "\e9e5";
}
.submail-v2-fingers-scroll-horizontal2:before {
  content: "\e9e6";
}
.submail-v2-fingers-scroll-vertical2:before {
  content: "\e9e7";
}
.submail-v2-fingers-scroll-left2:before {
  content: "\e9e8";
}
.submail-v2-fingers-scroll-right2:before {
  content: "\e9e9";
}
.submail-v2-fingers-scroll-vertical3:before {
  content: "\e9ea";
}
.submail-v2-border-style:before {
  content: "\e9eb";
}
.submail-v2-border-all:before {
  content: "\e9ec";
}
.submail-v2-border-outer:before {
  content: "\e9ed";
}
.submail-v2-border-inner:before {
  content: "\e9ee";
}
.submail-v2-border-top:before {
  content: "\e9ef";
}
.submail-v2-border-horizontal:before {
  content: "\e9f0";
}
.submail-v2-border-bottom:before {
  content: "\e9f1";
}
.submail-v2-border-left:before {
  content: "\e9f2";
}
.submail-v2-border-vertical:before {
  content: "\e9f3";
}
.submail-v2-border-right:before {
  content: "\e9f4";
}
.submail-v2-border-none:before {
  content: "\e9f5";
}
.submail-v2-ellipsis:before {
  content: "\e9f6";
}
