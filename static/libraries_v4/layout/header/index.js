$(document).ready(function () {
  nav = {
    init: function () {
      var _this = this;
      var pageUrl = window.location.pathname;
      if (pageUrl == '/events') {
        $('.v4_header_pc').addClass('whiteHeader');
        $('.v4_header_mob').addClass('whiteHeader');
      }
      if ($(window).scrollTop() > 60) {
        this.setScrollActive();
      }
      $(window).on('scroll', function (e) {
        var top = $(this).scrollTop();
        if (top > 60) {
          _this.setScrollActive();
        } else {
          _this.setScrollunActive();
        }
      });
    },
    setScrollActive: function () {
      $('.v4_header_pc').addClass('scrollActive');
      $('.v4_header_mob').addClass('scrollActive');
    },
    setScrollunActive: function () {
      $('.v4_header_pc').removeClass('scrollActive');
      $('.v4_header_mob').removeClass('scrollActive');
    },
  };
  nav_pc = {
    init: function () {
      $('.v4_header_pc').on('mouseenter', function () {
        $('.v4_header_pc').addClass('active');
      });
      $('.v4_header_pc').on('mouseleave', function () {
        $('.v4_header_pc').removeClass('active');
      });


      $('ul.top-nav>li').on('mouseenter', function () {
        var _this = this;
        $(_this).find('a').addClass('active');
        $(_this).find('.triangle').addClass('active');

        var _calssname = $(this).find('a').data('sub');
        if (_calssname) {
          $(`#${_calssname}`).attr("style", "").show().siblings().hide();
          $(`#${_calssname}`).find(".nav-drown-con").attr("style", "height: 100%; opacity: 1; transform: translate(0px, 0px);");

          $(`#${_calssname}`).on('mouseenter', function () {
            $(_this).find('a').addClass('active');
            $(_this).find('.triangle').addClass('active');
            $(`#${_calssname}`).attr("style", "").show().siblings().hide();
            $(`#${_calssname}`).find(".nav-drown-con").attr("style", "height: 100%; opacity: 1; transform: translate(0px, 0px);");
          });


          $(`#${_calssname}`).on('mouseleave', function () {
            $(`#${_calssname}`).attr("style", "visibility: hidden; height: 0px");
            $(`#${_calssname}`).find('.nav-drown-con').attr('style', 'height: 0; opacity: 0; transform: translate(0px, -100%);');
            $(_this).find('a').removeClass('active');
            $(_this).find('.triangle').removeClass('active');
          });

        }
      });
      $('ul.top-nav>li').on('mouseleave', function () {
        var _calssname = $(this).find('a').data('sub');
        $(this).find('a').removeClass('active');
        $(this).find('.triangle').removeClass('active');
        if (_calssname) {
          $(`#${_calssname}`).attr("style", "visibility: hidden; height: 0px");
          $(`#${_calssname}`).find('.nav-drown-con').attr('style', 'height: 0; opacity: 0; transform: translate(0px, -100%);');
        }
      });
    }
  };
  nav_mob = {
    init: function () {
      $('.v4_header_mob .right-menu').on('click', function () {
        $(this).toggleClass('active');

        $('.v4_header_mob').toggleClass('active');
        $('#nav_products').toggleClass('active1');

        $('.mob-nav-content .sidebar-fix').toggleClass('fixedActiveBg');
        $('.mob-nav-content .sidebar-fix').toggleClass('show');

        if ($('.mob-nav-content .sidebar-fix').hasClass('show')) {
          $('body').attr('style', 'overflow:hidden');
        } else {
          $('body').attr('style', 'overflow-y: scroll;');
        }
      });
    }
  };


  nav.init();
  nav_pc.init();
  nav_mob.init();


  // $(document).on('click', '.submail-mdi-nav-btn', function () {
  //   $('.mob-nav-content .sidebar-fix').toggleClass('fixedActiveBg');
  //   $('.mob-nav-content .sidebar-fix').toggleClass('show');

  //   if ($('.mob-nav-content .sidebar-fix').hasClass('show')) {
  //     $('body').attr('style', 'overflow:hidden');
  //   } else {
  //     $('body').attr('style', 'overflow-y: scroll;');
  //   }
  // });

  $(document).on('click', '.sidebar-fix-left .mob-nav-item', function () {
    $(this).addClass('active').siblings().removeClass('active');
    let _index = $(this).attr('data');
    $('.sidebar-fix-rigth .sub-nav').eq(_index).show().siblings().hide();
  });



});
