/* 
blockquote,
body,
dd,
div,
dl,
dt,
fieldset,
form,
h1,
h2,
h3,
h4,
h5,
h6,
html,
input,
li,
ol,
p,
pre,
ul {
  padding: 0;
  margin: 0;
}
address,
caption,
cite,
code,
em,
h1,
h2,
h3,
h4,
h5,
h6,
pre,
strong,
table,
td,
th {
  font-size: 1em;
  font-style: normal;
  font-weight: 400;
}
strong {
  font-weight: 700;
}
ol,
ul {
  list-style: none outside none;
}
fieldset,
img {
  border: medium none;
  vertical-align: bottom;
}
caption,
th {
  text-align: left;
}
table {
  border-collapse: collapse;
  border-spacing: 0;
}
body {
  background: none repeat scroll 0 0 #fff;
  color: #333;
  font: 12px/1 "微软雅黑", Tahoma, Helvetica, Arial, "\5b8b\4f53", sans-serif;
}
input,
select,
textarea {
  font: 12px/1 Tahoma, Helvetica, Arial, "\5b8b\4f53", sans-serif;
}
a {
  outline: medium none;
}
a:active,
a:link,
a:visited {
  text-decoration: none;
  color: #000;
}
a:hover {
  text-decoration: underline;
}
cite,
em,
i {
  font-styleign: center;
}
html {
  min-height: 101%;
}
.clearfix:after {
  clear: both;
  content: ".";
  display: block;
  height: 0;
  line-height: 0;
  visibility: hidden;
}
.clearfix {
  display: inline-block;
}
html[xmlns] .clearfix {
  display: block;
}
*html .clearfix {
  height: 1%;
}
time {
  color: #777;
}
article,
aside,
dialog,
figure,
footer,
header,
menu,
nav,
section {
  display: block;
}
ul {
  list-style: none;
}
a {
  text-decoration: none;
}
.clear {
  clear: both;
}
* {
  margin: 0;
  padding: 0;
}
.f-l {
  float: left;
}
.f-r {
  float: right;
}
.clearfix:after {
  content: "/20";
  display: block;
  visibility: hidden;
  clear: both;
  font-size: 0;
  line-height: 0;
  height: 0;
} */

@keyframes scaleToggleOne {
  0 {
    transform: scale(1);
    -webkit-transform: scale(1);
  }
  50% {
    transform: scale(2);
    -webkit-transform: scale(1.4, 1.2);
  }
  100% {
    transform: scale(1);
    -webkit-transform: scale(1);
  }
}
@keyframes scaleToggleTwo {
  0 {
    transform: scale(1);
    -webkit-transform: scale(1);
  }
  20% {
    transform: scale(1);
    -webkit-transform: scale(1);
  }
  60% {
    transform: scale(2);
    -webkit-transform: scale(1.4, 1.2);
  }
  100% {
    transform: scale(1);
    -webkit-transform: scale(1);
  }
}
@keyframes scaleToggleThree {
  0 {
    transform: scale(1);
    -webkit-transform: scale(1);
  }
  33% {
    transform: scale(1);
    -webkit-transform: scale(1);
  }
  66% {
    transform: scale(2);
    -webkit-transform: scale(1.4, 1.2);
  }
  100% {
    transform: scale(1);
    -webkit-transform: scale(1);
  }
}
.animated {
  -webkit-animation-duration: 0.5s;
  animation-duration: 0.5s;
  -webkit-animation-fill-mode: both;
  animation-fill-mode: both;
}
.livechat-girl {
  width: 54px;
  height: 140px;
  border-radius: 50px;
  position: relative;
  right: -3rem;
  opacity: 0;
  -webkit-box-shadow: 0 5px 10px 0 rgba(35, 50, 56, 0.3);
  box-shadow: 0 5px 10px 0 rgba(35, 50, 56, 0.3);
  z-index: 700;
  transform: translateY(0);
  -webkit-transform: translateY(0);
  -ms-transform: translateY(0);
  cursor: pointer;
  -webkit-transition: all 1s cubic-bezier(0.86, 0, 0.07, 1);
  transition: all 1s cubic-bezier(0.86, 0, 0.07, 1);
  background: linear-gradient(to bottom, #4d88ff, #1764ff);
}
.livechat-girl:focus {
  outline: 0;
}
.livechat-girl.animated {
  opacity: 1;
  transform: translateY(-40px);
  -webkit-transform: translateY(-40px);
  -ms-transform: translateY(-40px);
}
/* .livechat-girl:after {
  content: "";
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background-image: linear-gradient(to bottom, #26c7fc, #26c7fc);
  position: absolute;
  right: 1px;
  top: 1px;
  z-index: 50;
} */
.livechat-girl .girl {
  position: absolute;
  top: 0;
  left: 0;
  width: 20px;
  height: auto;
  z-index: 50;
  transform: translate(17px, 20px);
}
.livechat-girl .animated-circles .circle {
  /* background: rgba(38, 199, 252, 0.25); */
  background: rgba(60, 100, 231, 0.25);
  width: 54px;
  height: 140px;
  border-radius: 50px;
  position: absolute;
  z-index: 49;
  transform: scale(1);
  -webkit-transform: scale(1);
}
.livechat-girl .animated-circles.animated .c-1 {
  animation: 2s scaleToggleOne cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
}
.livechat-girl .animated-circles.animated .c-2 {
  animation: 2.5s scaleToggleTwo cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
}
.livechat-girl .animated-circles.animated .c-3 {
  animation: 3s scaleToggleThree cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
}
/* .livechat-girl.animation-stopped .circle {
  opacity: 0 !important;
}
.livechat-girl.animation-stopped .circle {
  opacity: 0 !important;
} */
.livechat-girl .livechat-hint {
  position: absolute;
  right: 40px;
  top: 50%;
  margin-top: -20px;
  opacity: 0;
  z-index: 0;
  -webkit-transition: all 0.3s cubic-bezier(0.86, 0, 0.07, 1);
  transition: all 0.3s cubic-bezier(0.86, 0, 0.07, 1);
}
.livechat-girl .livechat-hint.show_hint {
  -webkit-transform: translateX(-40px);
  transform: translateX(-40px);
  opacity: 1;
}
.livechat-girl .livechat-hint.hide_hint {
  opacity: 0;
  -webkit-transform: translateX(0);
  transform: translateX(0);
}
.livechat-girl .livechat-hint.rd-notice-tooltip {
  max-width: 1296px !important;
}
.livechat-girl .livechat-hint.rd-notice-tooltip .rd-notice-content {
  width: auto;
  overflow: hidden;
  text-overflow: ellipsis;
}
@media only screen and (max-width: 1599px) {
  .livechat-girl .livechat-hint.rd-notice-tooltip {
    max-width: 1060px !important;
  }
}
@media only screen and (max-width: 1309px) {
  .livechat-girl .livechat-hint.rd-notice-tooltip {
    max-width: 984px !important;
  }
}
@media only screen and (max-width: 1124px) {
  .livechat-girl .livechat-hint.rd-notice-tooltip {
    max-width: 600px !important;
  }
}
.rd-notice-tooltip {
  -webkit-box-shadow: 0 2px 2px rgba(0, 0, 0, 0.2);
  box-shadow: 0 2px 2px rgba(0, 0, 0, 0.2);
  font-size: 14px;
  border-radius: 3px;
  line-height: 1.25;
  position: absolute;
  z-index: 65;
  max-width: 350px;
  opacity: 1;
}
.rd-notice-tooltip:after {
  position: absolute;
  display: block;
  content: "";
  height: 20px;
  width: 20px;
  -webkit-box-shadow: none;
  box-shadow: none;
  -webkit-transform: rotate(-45deg);
  -moz-transform: rotate(-45deg);
  -ms-transform: rotate(-45deg);
  -o-transform: rotate(-45deg);
  transform: rotate(-45deg);
  -webkit-border-radius: 3px;
  -moz-border-radius: 3px;
  border-radius: 3px;
  z-index: 50;
}
.rd-notice-tooltip .rd-notice-content {
  background: 0;
  border-radius: 3px;
  width: 100%;
  color: #fff;
  position: relative;
  z-index: 60;
  padding: 20px;
  font-weight: 400;
  line-height: 1.45;
}
.rd-notice-type-success {
  background-color: #26c7fc;
  -webkit-box-shadow: 0 5px 10px 0 rgba(38, 199, 252, 0.2);
  box-shadow: 0 5px 10px 0 rgba(38, 199, 252, 0.2);
}
.rd-notice-type-success .rd-notice-content {
  background-color: #26c7fc;
}
/* .rd-notice-type-success:after {
  background-color: #26c7fc;
  -webkit-box-shadow: 0 5px 10px 0 rgba(38, 199, 252, 0.2);
  box-shadow: 0 5px 10px 0 rgba(38, 199, 252, 0.2);
} */
.rd-notice-position-left {
  margin-left: -20px;
}
/* .rd-notice-position-left:after {
  right: -6px;
  top: 50%;
  margin-top: -10px;
} */
.rd-notice-tooltip.single-line .rd-notice-content {
  height: 40px;
  padding: 0 20px;
  line-height: 40px;
  white-space: nowrap;
}
.ask-ke {
  background: #1764ff;
  width: 100px;
  padding: 10px 0;
  color: #fff;
  font-weight: bold;
  border-radius: 4px;
  text-align: center;
  margin-top: 20px;
}
