$(document).ready(function () {
    var banner = {
        activeIndex: 0,
        timer: null,
        list: [
            {
                _src:'/about',
                dot: "关于思而听",
                bg: '/static/libraries_v4/view/home/<USER>/banner_1.png',
                mb_bg: '/static/libraries_v4/view/home/<USER>/m_1.png',
                content: `
                  <h3 class="title animated fadeIn">思而听网络科技 <span style='color:#1764ff'>守护数字世界</span></h3>
                  <span class="text animated fadeIn">专业网络安全服务提供商，为政府、金融、企业提供全方位安全防护解决方案</span>
                  <a href="/about" class="button mt-5 animated fadeIn konw-detail">了解详情</a>
              `,
            },

            {
                _src:'/products/training-system',
                dot: "安全产品",
                bg: '/static/libraries_v4/view/home/<USER>/banner_2.jpg',
                mb_bg: '/static/libraries_v4/view/home/<USER>/m_2.png',
                content: `
                  <h3 class="title animated fadeIn">知行·天狩培训系统 <span style='color:#1764ff'>学训赛研一体化</span></h3>
                  <span class="text animated fadeIn">千道自创题型，3D街景攻防态势，专业网络安全人才培养平台</span>
                  <a href="/products/training-system" class="button mt-5 animated fadeIn konw-detail">预约演示</a>
              `,
            },
            {
                _src:'/services/security-operation',
                dot: "安全服务",
                bg: '/static/libraries_v4/view/home/<USER>/banner_3.jpg',
                mb_bg: '/static/libraries_v4/view/home/<USER>/m_3.png',
                content: `
                  <h3 class="title animated fadeIn" style="margin-top:12px">7x24小时安全监测 专业运营服务</h3>
                  <span class="text animated fadeIn">数百人专业安全团队，为100+国家重大活动提供安保服务支撑，全部实现安全0事故</span>
                  <div class="pro-sms konw-detail">
                    <div class="fav mt-2 m-0">
                        <span class="iconfont4 icon-liwu fn12 ml-2 mr-2"></span>
                        <span class="fn14">应急响应服务，快速恢复数据，修复漏洞 <span class="line-1"></span> <a href="/services/emergency-response" style="color:#fff"> 了解详情 <span class="iconfont4 icon-jiantou_1 gt"></span></a> </span>
                    </div>
                  </div>
                  <a href="/services/security-operation" class="button mt-3 animated fadeIn konw-detail">了解详情</a>
              `,
            },
            {
                _src:'/solutions/government',
                dot: "解决方案",
                bg: '/static/libraries_v4/view/home/<USER>/banner_4.jpg',
                mb_bg: '/static/libraries_v4/view/home/<USER>/m_4.png',
                content: `
                  <h3 class="title animated fadeIn">行业解决方案 全方位安全防护</h3>
                  <span class="text animated fadeIn">面向政府、金融、运营商、大型企业、互联网、医疗机构等行业提供专业的网络安全解决方案</span>
                  <a href="/solutions/government" class="button mt-5 animated fadeIn konw-detail">获取方案</a>
              `,
            },
        ],
        init: function () {
            this.initDots();
            this.setActive(0);
        },
        initDots: function () {
            var _this = this;
            var lis = '';
            this.list.forEach(function (item, index) {
                var _li = `
                  <li class="dot-item" data-index="${index}">
                      <span class="dot-text">${item.dot}</span>
                      <i class="dot-line"></i>
                  </li>
              `;
                lis += _li;
            });
            $('.top-banner-section .banner-dots').empty().append(lis);
            $('.top-banner-section .dot-item').on('click', function () {
                var index = $(this).data('index');
                if (index == _this.activeIndex) return;
                _this.setActive(index);
            });

        },
        setActive: function (index) {
            if (this.timer) clearTimeout(this.timer);
            var _this = this;

            $('.top-banner-section .banner-dots').children().each(function () {
                $(this).removeClass("active");
            }).eq(index).addClass("active");
            $('.top-banner-section .banner-item').empty().hide().append(this.list[index].content).fadeIn(500);


            $('.pc-banner-img img').fadeOut(200, function () {
                $(this).attr('src', _this.list[index].bg).fadeIn(300);
            });
            $('.mb-banner-img img').fadeOut(200, function () {
                $(this).attr('src', _this.list[index].mb_bg).fadeIn(300);
            });
            $('.mb-banner-img img').attr('data', _this.list[index]._src)


            if (window.matchMedia('(max-width: 576px)').matches) {
                $(document).on('click','.top-banner-section',function(){
                    let targetUrl =$(this).find('.mb-banner-img img').attr('data');
                    window.location.href = targetUrl;
                })
            }

            // $('.top-banner-section').css('background-image', `url(${banner.list[index].bg})`);

            this.activeIndex = index;
            this.timer = setTimeout(function () {
                if (_this.activeIndex == _this.list.length - 1) {
                    _this.setActive(0);
                } else {
                    _this.setActive(_this.activeIndex + 1);
                }
            }, 6000);
        }
    };
    var free = {
        init: function () {
            var mobtest = /^1[0-9]{10}/;
            var _this = this;
            $('.send_free_btn').on('click', function () {
                var that = this;
                $(that).attr('disabled', 'disabled');
                $(that).text('正在发送');
                var send = {
                    mob: $('input[name=test_mobile]').val().replace(/ /g, ''),
                    code: $('input[name=test_code]').val()
                };
                if (!mobtest.test(send.mob)) {

                    alert('请填写您的手机号码！');
                    $('.send_free_btn').removeAttr('disabled');
                    $(that).text('免费体验');
                } else {
                    if (send.mob.length == 11) {
                        $.post('/global/products/sendTest', send, function (data) {
                            var rel = $.parseJSON(data);
                            if (rel.returns == "true") {
                                if (rel.msg == 'true') {
                                    alert('发送成功，请注意查收');

                                    _this.vdsend('重新发送', $('.send_free_btn'), 30);
                                    $('.reloadverifycode').find('img').attr('src', '/global/products/codeImg?' + Math.random());
                                } else {
                                    alert('手机号或IP测试次数已达上限！');
                                    // $('input[name=test_mobile]').val('');
                                    // $('input[name=test_code]').val('');
                                    $('.send_free_btn').removeAttr('disabled');
                                    $(that).text('免费体验');
                                    $('.reloadverifycode').find('img').attr('src', '/global/products/codeImg?' + Math.random());
                                }
                            } else {
                                alert('验证码错误，请重新输入');
                                $('.send_free_btn').removeAttr('disabled');
                                $(that).text('免费体验');
                                $('.reloadverifycode').find('img').attr('src', '/global/products/codeImg?' + Math.random());
                            }
                        });
                    } else {
                        alert('手机号码不正确！');
                        $('.send_free_btn').removeAttr('disabled');
                        $(that).text('免费体验');
                    }
                }
            });
            /*
             *点击图片刷新
             */
            $('.reloadverifycode').on('click', function () {
                $(this).find('img').attr('src', '/global/products/codeImg?' + Math.random());
            });
        },
        // 设置测试间隔
        vdsend: function (s, e, t) {
            var _this = this;
            setTimeout(function () {
                if (t > 0) {
                    t -= 1;
                    e.text(s + '（' + t + '）');
                    _this.vdsend(s, e, t);
                } else {
                    e.text(s);
                    e.removeAttr('disabled');
                }
            }, 1000);
        }
    };
    var nav = {
        activeNavIndex: 0,
        activeSubNavIndex: 0,
        list: [
            {
                "title": "政府机构",
                "children": [
                    {
                        "title": "电子政务云安全",
                        "icon": "/static/libraries_v4/view/home/<USER>/homeicon/school_1.png",
                        "mobile": "/static/libraries_v4/view/home/<USER>/homeicon/nobiao.png",
                        "mb_news": "/static/libraries_v4/view/home/<USER>/teach/smsartSchool.png",
                        "new_text1": "7x24小时安全监测",
                        "new_text2": "主动防御",
                        "news_text_index1": 'img-txt-style1',
                        "news_text_index2": 'img-txt-style2',

                        "content": `
                          <h5 class="sub-nav-title">电子政务云安全</h5>
                          <p class="sub-nav-text mt-3">为政府机构提供全方位的网络安全防护，保障电子政务系统安全稳定运行，提升政府数字化转型的安全保障能力。</p>
                          <div class="case mt-3">
                              <div class="case-left">
                                    <div class="case-logo mr-3 ml-2">
                                        <img src="/static/libraries_v4/view/home/<USER>/section_4/logo/fudan.png" alt=""/>
                                    </div>
                                  <div class="case-info">
                                        <a href="/solutions/government"><div class="case-name d-flex align-items-center">某省政府 <span class="ml-1 icon iconfont4 icon-jiantou_1 roda-arrow"></div> </a>
                                        <div class="case-text mt-1">国家“双一流”、“985工程”的综合性高校</div>
                                  </div>

                              </div>
                              <div class="case-rate ">
                                    <div class="rate-item mr-3">
                                        <div class="rate-num d-flex align-items-center">
                                            <span class="number" data-target="30"></span>
                                                <div class="ml-1 text-center num-info">
                                                    <img class="up-down roat-deg" src="/static/libraries_v4/view/home/<USER>/down.svg" />
                                                    <div class="fn18 color-default">%</div>
                                                </div>
                                            </div>
                                        <div class="rate-name fn12">教育数智化</div>
                                    </div>
                                    <div class="rate-item mr-2">
                                        <div class="rate-num d-flex align-items-center">
                                            <span class="number" data-target="3"></span>
                                                <div class="ml-1 text-center num-info">
                                                    <img class="up-down " src="/static/libraries_v4/view/home/<USER>/down.svg" />
                                                    <div class="fn18 color-default">s</div>
                                                </div>
                                            </div>
                                        <div class="rate-name fn12">发送速度</div>
                                    </div>
                              </div>
                          </div>
                          <p class="about-title mt-4 mb-0">相关产品</p>
                          <ul class="about-list"></ul>
                      `
                    },
                    {
                        "title": "在线教育",
                        "icon": "/static/libraries_v4/view/home/<USER>/homeicon/school_3.png",
                        "mb_news": "/static/libraries_v4/view/home/<USER>/teach/online.png",
                        "mobile": "/static/libraries_v4/view/home/<USER>/homeicon/xuanfu.png",
                        "new_text1": "5G阅信 助力在线教育",
                        "new_text2": "悬浮菜单",
                        "news_text_index1": 'img-txt-style3',
                        "news_text_index2": 'img-txt-style4',
                        "content": `
                          <h5 class="sub-nav-title">在线教育</h5>
                          <p class="sub-nav-text mt-3">云通信服务在保障用户学习环境和信息安全的同时，又可向学生发送课程预告、作业考试等信息，提高学生的学习效率和管理效率，是在线教育不可或缺的一部分。</p>
                          <div class="case mt-3">
                              <div class="case-left">
                                    <div class="case-logo mr-3 ml-2">
                                        <img src="/static/libraries_v4/view/home/<USER>/section_4/logo/muke.png" alt=""/>
                                    </div>
                                  <div class="case-info">
                                        <a href="/education"><div class="case-name d-flex align-items-center">慕课网 <span class="ml-1 icon iconfont4 icon-jiantou_1 roda-arrow"></div> </a>
                                        <div class="case-text mt-1">国内领先的 IT 技能在线学习平台</div>
                                  </div>

                              </div>
                              <div class="case-rate ">
                                    <div class="rate-item mr-3">
                                        <div class="rate-num d-flex align-items-center">
                                            <span class="number" data-target="28"></span>
                                                <div class="ml-1 text-center num-info">
                                                    <img class="up-down roat-deg" src="/static/libraries_v4/view/home/<USER>/down.svg" />
                                                    <div class="fn18 color-default">%</div>
                                                </div>
                                            </div>
                                        <div class="rate-name fn12">响应速度</div>
                                    </div>
                                    <div class="rate-item mr-2">
                                        <div class="rate-num d-flex align-items-center">
                                            <span class="number" data-target="40"></span>
                                                <div class="ml-1 text-center num-info">
                                                    <img class="up-down roat-deg" src="/static/libraries_v4/view/home/<USER>/down.svg" />
                                                    <div class="fn18 color-default">%</div>
                                                </div>
                                            </div>
                                        <div class="rate-name fn12">安全性</div>
                                    </div>
                              </div>
                          </div>
                          <p class="about-title mt-4 mb-0">相关产品</p>
                          <ul class="about-list"></ul>
                      `
                    },
                    {
                        "title": "k12教育",
                        "icon": "/static/libraries_v4/view/home/<USER>/homeicon/school_2.png",
                        "mb_news": "/static/libraries_v4/view/home/<USER>/teach/k12.png",
                        "mobile": "/static/libraries_v4/view/home/<USER>/homeicon/daibiao.png",
                        "new_text1": "教育行业通信管理平台",
                        "new_text2": "高到达率",
                        "news_text_index1": 'img-txt-style5',
                        "news_text_index2": 'img-txt-style6',

                        "content": `
                          <h5 class="sub-nav-title">k12教育</h5>
                          <p class="sub-nav-text mt-3">方便家长及时了解学校活动安排和孩子课程进度，促进家长和孩子之间的互动交流。在兼顾低成本和高效率的同时，增强对k12教育的了解和认可，提升招生入学率。</p>
                          <div class="case mt-3">
                              <div class="case-left">
                                    <div class="case-logo mr-3 ml-2">
                                        <img src="/static/libraries_v4/view/home/<USER>/section_4/logo/yf.png" alt=""/>
                                    </div>
                                  <div class="case-info">
                                        <a href="/education"><div class="case-name d-flex align-items-center">英孚教育 <span class="ml-1 icon iconfont4 icon-jiantou_1 roda-arrow"></div> </a>
                                        <div class="case-text mt-1">提供语言学习等课程的全球知名教育机构</div>
                                  </div>

                              </div>
                              <div class="case-rate ">
                                    <div class="rate-item mr-3">
                                        <div class="rate-num d-flex align-items-center">
                                            <span class="number" data-target="12"></span>
                                                <div class="ml-1 text-center num-info">
                                                    <img class="up-down roat-deg" src="/static/libraries_v4/view/home/<USER>/down.svg" />
                                                    <div class="fn18 color-default">%</div>
                                                </div>
                                            </div>
                                        <div class="rate-name fn12">入学率</div>
                                    </div>
                                    <div class="rate-item mr-2">
                                        <div class="rate-num d-flex align-items-center">
                                            <span class="number" data-target="20"></span>
                                                <div class="ml-1 text-center num-info">
                                                    <img class="up-down" src="/static/libraries_v4/view/home/<USER>/down.svg" />
                                                    <div class="fn18 color-default">%</div>
                                                </div>
                                            </div>
                                        <div class="rate-name fn12">推广成本</div>
                                    </div>
                              </div>
                          </div>
                          <p class="about-title mt-4 mb-0">相关产品</p>
                          <ul class="about-list"></ul>
                      `
                    },
                ]
            },
            {
                "title": "电商行业",
                "children": [
                    {
                        "title": "平台电商",
                        "icon": "/static/libraries_v4/view/home/<USER>/homeicon/dian1.png",

                        "mobile": "/static/libraries_v4/view/home/<USER>/homeicon/nobiao.png",
                        "mb_news": "/static/libraries_v4/view/home/<USER>/shop/ping.png",
                        "new_text1": "106码号",
                        "new_text2": "国内短信营销推广",
                        "news_text_index1": 'img-txt-style7',
                        "news_text_index2": 'img-txt-style8',
                        "class_name": 'pingtai-shop',

                        "content": `
                          <h5 class="sub-nav-title">平台电商</h5>
                          <p class="sub-nav-text mt-3">通过云通信服务及时解决客户的问题，提高客户对商家的满意度和忠诚度，可有效提升平台电商的用户体验和运营效率，同时也为电商平台提供了信息安全保障。</p>
                          <div class="case mt-3">
                              <div class="case-left">
                                    <div class="case-logo mr-3 ml-2">
                                        <img src="/static/libraries_v4/view/home/<USER>/section_4/logo/jd.png" alt=""/>
                                    </div>
                                  <div class="case-info">
                                        <a href="/solution"><div class="case-name d-flex align-items-center">京东商城 <span class="ml-1 icon iconfont4 icon-jiantou_1 roda-arrow"></div> </a>
                                        <div class="case-text mt-1">电子商务领域受消费者欢迎的综合网购平台</div>
                                  </div>

                              </div>
                              <div class="case-rate ">
                                    <div class="rate-item mr-3">
                                        <div class="rate-num d-flex align-items-center">
                                            <span class="number" data-target="20"></span>
                                                <div class="ml-1 text-center num-info">
                                                    <img class="up-down roat-deg" src="/static/libraries_v4/view/home/<USER>/down.svg" />
                                                    <div class="fn18 color-default">%</div>
                                                </div>
                                            </div>
                                        <div class="rate-name fn12">复购率</div>
                                    </div>
                                    <div class="rate-item mr-2">
                                        <div class="rate-num d-flex align-items-center">
                                            <span class="number" data-target="30"></span>
                                                <div class="ml-1 text-center num-info">
                                                    <img class="up-down " src="/static/libraries_v4/view/home/<USER>/down.svg" />
                                                    <div class="fn18 color-default">%</div>
                                                </div>
                                            </div>
                                        <div class="rate-name fn12">推广成本</div>
                                    </div>
                              </div>
                          </div>
                          <p class="about-title mt-4 mb-0">相关产品</p>
                          <ul class="about-list"></ul>
                      `
                    },
                    {
                        "title": "跨境电商",
                        "icon": "/static/libraries_v4/view/home/<USER>/homeicon/dian2.png",

                        "mobile": "/static/libraries_v4/view/home/<USER>/homeicon/daibiao.png",
                        "mb_news": "/static/libraries_v4/view/home/<USER>/shop/kua.png",
                        "new_text1": "图文并茂",
                        "new_text2": "全能格式 多媒体彩信",
                        "news_text_index1": 'img-txt-style9',
                        "news_text_index2": 'img-txt-style10',

                        "content": `
                          <h5 class="sub-nav-title">跨境电商</h5>
                          <p class="sub-nav-text mt-3">拓展海外市场和提高品牌知名度是跨境电商的核心所在。通过云通信平台，可提升目标受众集中度，快速吸引更多潜在客户，全方位提高曝光率、影响力和实际效率。</p>
                          <div class="case mt-3">
                              <div class="case-left">
                                    <div class="case-logo mr-3 ml-2">
                                        <img src="/static/libraries_v4/view/home/<USER>/section_4/logo/la.png" alt=""/>
                                    </div>
                                  <div class="case-info">
                                        <a href="/solution"><div class="case-name d-flex align-items-center">Lazada <span class="ml-1 icon iconfont4 icon-jiantou_1 roda-arrow"></div> </a>
                                        <div class="case-text mt-1">东南亚区域人人爱用的在线跨境电商平台</div>
                                  </div>

                              </div>
                              <div class="case-rate ">
                                    <div class="rate-item mr-3">
                                        <div class="rate-num d-flex align-items-center">
                                            <span class="number" data-target="30"></span>
                                                <div class="ml-1 text-center num-info">
                                                    <img class="up-down roat-deg" src="/static/libraries_v4/view/home/<USER>/down.svg" />
                                                    <div class="fn18 color-default">s</div>
                                                </div>
                                            </div>
                                        <div class="rate-name fn12">品牌知名度</div>
                                    </div>
                                    <div class="rate-item mr-2">
                                        <div class="rate-num d-flex align-items-center">
                                            <span class="number" data-target="50"></span>
                                                <div class="ml-1 text-center num-info">
                                                    <img class="up-down roat-deg" src="/static/libraries_v4/view/home/<USER>/down.svg" />
                                                    <div class="fn18 color-default">%</div>
                                                </div>
                                            </div>
                                        <div class="rate-name fn12">目标受众集中度</div>
                                    </div>
                              </div>
                          </div>
                          <p class="about-title mt-4 mb-0">相关产品</p>
                          <ul class="about-list"></ul>
                      `
                    },
                    {
                        "title": "新零售 +",
                        "icon": "/static/libraries_v4/view/home/<USER>/homeicon/dian3.png",

                        "mobile": "/static/libraries_v4/view/home/<USER>/homeicon/daibiao.png",
                        "mb_news": "/static/libraries_v4/view/home/<USER>/shop/xin.png",
                        "new_text1": "品牌展示",
                        "new_text2": "5G 阅信 富媒体卡片",
                        "news_text_index1": 'img-txt-style7',
                        "news_text_index2": 'img-txt-style11',

                        "content": `
                          <h5 class="sub-nav-title">新零售 +</h5>
                          <p class="sub-nav-text mt-3">助力商家拓展客群，协助商家解决在移动互联网时代遇到的推广获客、成交转化、复购增长、分享裂变等问题，实现渠道数字化、智能化和国际化。</p>
                          <div class="case mt-3">
                              <div class="case-left">
                                    <div class="case-logo mr-3 ml-2">
                                        <img src="/static/libraries_v4/view/home/<USER>/section_4/logo/yz.png" alt=""/>
                                    </div>
                                  <div class="case-info">
                                        <a href="/solution"><div class="case-name d-flex align-items-center">有赞 <span class="ml-1 icon iconfont4 icon-jiantou_1 roda-arrow"></div> </a>
                                        <div class="case-text mt-1">提供全行业解决方案的 SaaS 服务提供商</div>
                                  </div>

                              </div>
                              <div class="case-rate ">
                                    <div class="rate-item mr-3">
                                        <div class="rate-num d-flex align-items-center">
                                            <span class="number" data-target="20"></span>
                                                <div class="ml-1 text-center num-info">
                                                    <img class="up-down " src="/static/libraries_v4/view/home/<USER>/down.svg" />
                                                    <div class="fn18 color-default">%</div>
                                                </div>
                                            </div>
                                        <div class="rate-name fn12">推广成本</div>
                                    </div>
                                    <div class="rate-item mr-2">
                                        <div class="rate-num d-flex align-items-center">
                                            <span class="number" data-target="30"></span>
                                                <div class="ml-1 text-center num-info">
                                                    <img class="up-down roat-deg" src="/static/libraries_v4/view/home/<USER>/down.svg" />
                                                    <div class="fn18 color-default">%</div>
                                                </div>
                                            </div>
                                        <div class="rate-name fn12">拓客速度</div>
                                    </div>
                              </div>
                          </div>
                          <p class="about-title mt-4 mb-0">相关产品</p>
                          <ul class="about-list"></ul>
                      `
                    },
                ]
            },
            {
                "title": "政企行业",
                "children": [

                    {
                        "title": "G2B 管理",
                        "icon": "/static/libraries_v4/view/home/<USER>/homeicon/gov2.png",

                        "mobile": "/static/libraries_v4/view/home/<USER>/homeicon/nobiao.png",
                        "mb_news": "/static/libraries_v4/view/home/<USER>/gove/G2B.png",
                        "new_text1": "安全稳定三网通道",
                        "new_text2": "五秒必达",
                        "news_text_index1": 'img-txt-style16',
                        "news_text_index2": 'img-txt-style17',
                        "class_name": "gove-g2b",

                        "content": `
                          <h5 class="sub-nav-title">G2B 管理</h5>
                          <p class="sub-nav-text mt-3">通过云通信平台，加强与企业的双向互动，构建新型政商关系。优化营商环境的同时，也更方便地对企业进行监管，促进经济发展和社会进步。</p>
                          <div class="case mt-3">
                              <div class="case-left">
                                    <div class="case-logo mr-3 ml-2">
                                        <img src="/static/libraries_v4/view/home/<USER>/section_4/logo/wbank.png" alt=""/>
                                    </div>
                                  <div class="case-info">
                                        <a href="/gover"><div class="case-name d-flex align-items-center">威海商业银行 <span class="ml-1 icon iconfont4 icon-jiantou_1 roda-arrow"></div> </a>
                                        <div class="case-text mt-1">威海市股份制“全国十佳城市商业银行”</div>
                                  </div>

                              </div>
                              <div class="case-rate ">
                                    <div class="rate-item mr-3">
                                        <div class="rate-num d-flex align-items-center">
                                            <span class="number" data-target="20"></span>
                                                <div class="ml-1 text-center num-info">
                                                    <img class="up-down roat-deg" src="/static/libraries_v4/view/home/<USER>/down.svg" />
                                                    <div class="fn18 color-default">%</div>
                                                </div>
                                            </div>
                                        <div class="rate-name fn12">政企互信度</div>
                                    </div>
                                    <div class="rate-item mr-2">
                                        <div class="rate-num d-flex align-items-center">
                                            <span class="number" data-target="28"></span>
                                                <div class="ml-1 text-center num-info">
                                                    <img class="up-down roat-deg" src="/static/libraries_v4/view/home/<USER>/down.svg" />
                                                    <div class="fn18 color-default">%</div>
                                                </div>
                                            </div>
                                        <div class="rate-name fn12">政务处理速度</div>
                                    </div>
                              </div>
                          </div>
                          <p class="about-title mt-4 mb-0">相关产品</p>
                          <ul class="about-list"></ul>
                      `
                    },
                    {
                        "title": "G2G 管理",
                        "icon": "/static/libraries_v4/view/home/<USER>/homeicon/gov1.png",

                        "mobile": "/static/libraries_v4/view/home/<USER>/gove/G2G.jpg",
                        "mb_news": "",
                        "new_text1": "高效、专业、易用",
                        "new_text2": "TLS 加密传输",
                        "news_text_index1": 'img-txt-style18',
                        "news_text_index2": 'img-txt-style19',
                        "class_name": "gove-g2g",

                        "content": `
                          <h5 class="sub-nav-title">G2G 管理</h5>
                          <p class="sub-nav-text mt-3">提升政府各机构之间的沟通效率、协作能力和响应速度，推进电子政务建设，加强政府数字化转型和上下级协调和管理，实现 G2G管理自动化、透明化、高效化。</p>
                          <div class="case mt-3">
                              <div class="case-left">
                                    <div class="case-logo mr-3 ml-2">
                                        <img src="/static/libraries_v4/view/home/<USER>/section_4/logo/wh.png" alt=""/>
                                    </div>
                                  <div class="case-info">
                                        <a href="/gover"><div class="case-name d-flex align-items-center">武汉市文化和旅游局 <span class="ml-1 icon iconfont4 icon-jiantou_1 roda-arrow"></div> </a>
                                        <div class="case-text mt-1">武汉市人民政府职能工作部门</div>
                                  </div>

                              </div>
                              <div class="case-rate ">
                                    <div class="rate-item mr-3">
                                        <div class="rate-num d-flex align-items-center">
                                            <span class="number" data-target="30"></span>
                                                <div class="ml-1 text-center num-info">
                                                    <img class="up-down roat-deg" src="/static/libraries_v4/view/home/<USER>/down.svg" />
                                                    <div class="fn18 color-default">%</div>
                                                </div>
                                            </div>
                                        <div class="rate-name fn12">部门协作效率</div>
                                    </div>
                                    <div class="rate-item mr-2">
                                        <div class="rate-num d-flex align-items-center">
                                            <span class="number" data-target="38"></span>
                                                <div class="ml-1 text-center num-info">
                                                    <img class="up-down roat-deg" src="/static/libraries_v4/view/home/<USER>/down.svg" />
                                                    <div class="fn18 color-default">%</div>
                                                </div>
                                            </div>
                                        <div class="rate-name fn12">数智化程度</div>
                                    </div>
                              </div>
                          </div>
                          <p class="about-title mt-4 mb-0">相关产品</p>
                          <ul class="about-list"></ul>
                      `
                    },
                    {
                        "title": "G2C 管理",
                        "icon": "/static/libraries_v4/view/home/<USER>/homeicon/gov3.png",

                        "mobile": "/static/libraries_v4/view/home/<USER>/homeicon/factor.png",
                        "mb_news": "/static/libraries_v4/view/home/<USER>/gove/G2C.png",
                        "new_text1": "直连运营商和公安数据库",
                        "new_text2": "",
                        "news_text_index1": 'img-txt-style20',
                        "news_text_index2": 'img-txt-style21',
                        "class_name": "factorimg",


                        "content": `
                          <h5 class="sub-nav-title">G2C 管理</h5>
                          <p class="sub-nav-text mt-3">政府可以向个人发送各类信息，以便民众及时了解配合政府相关工作开展；民众也可通过云通信服务向政府反馈问题和建议，帮助政府更好地解决社会问题。</p>
                          <div class="case mt-3">
                              <div class="case-left">
                                    <div class="case-logo mr-3 ml-2">
                                        <img src="/static/libraries_v4/view/home/<USER>/section_4/logo/gz.png" alt=""/>
                                    </div>
                                  <div class="case-info">
                                        <a href="/gover"><div class="case-name d-flex align-items-center">广州地铁 <span class="ml-1 icon iconfont4 icon-jiantou_1 roda-arrow"></div> </a>
                                        <div class="case-text mt-1">一体化、便捷化、人性化的轨道交通服务</div>
                                  </div>

                              </div>
                              <div class="case-rate ">
                                    <div class="rate-item mr-3">
                                        <div class="rate-num d-flex align-items-center">
                                            <span class="number" data-target="22"></span>
                                                <div class="ml-1 text-center num-info">
                                                    <img class="up-down roat-deg" src="/static/libraries_v4/view/home/<USER>/down.svg" />
                                                    <div class="fn18 color-default">%</div>
                                                </div>
                                            </div>
                                        <div class="rate-name fn12">信息传播度</div>
                                    </div>
                                    <div class="rate-item mr-2">
                                        <div class="rate-num d-flex align-items-center">
                                            <span class="number" data-target="36"></span>
                                                <div class="ml-1 text-center num-info">
                                                    <img class="up-down roat-deg" src="/static/libraries_v4/view/home/<USER>/down.svg" />
                                                    <div class="fn18 color-default">s</div>
                                                </div>
                                            </div>
                                        <div class="rate-name fn12">民众参与度</div>
                                    </div>
                              </div>
                          </div>
                          <p class="about-title mt-4 mb-0">相关产品</p>
                          <ul class="about-list"></ul>
                      `
                    },
                ]
            },
            {
                "title": "游戏行业",
                "children": [
                    {
                        "title": "游戏研发",
                        "icon": "/static/libraries_v4/view/home/<USER>/homeicon/game1.png",

                        "mobile": "/static/libraries_v4/view/home/<USER>/homeicon/nobiao.png",
                        "mb_news": "/static/libraries_v4/view/home/<USER>/game/img1.png",
                        "new_text1": "MaaP 消息平台",
                        "new_text2": "5秒必达",
                        "news_text_index1": 'img-txt-style12',
                        "news_text_index2": 'img-txt-style13',

                        "content": `
                          <h5 class="sub-nav-title">游戏研发</h5>
                          <p class="sub-nav-text mt-3">云通信服务在游戏研发时，可用于邀请玩家参与测试调研；在游戏运营中，可用于处理玩家的问题、投诉或建议。可以提高玩家的参与度和留存率，助力市场表现。</p>
                          <div class="case mt-3">
                              <div class="case-left">
                                    <div class="case-logo mr-3 ml-2">
                                        <img src="/static/libraries_v4/view/home/<USER>/section_4/logo/wy.png" alt=""/>
                                    </div>
                                  <div class="case-info">
                                        <a href="/solugame"><div class="case-name d-flex align-items-center">网易游戏 <span class="ml-1 icon iconfont4 icon-jiantou_1 roda-arrow"></div> </a>
                                        <div class="case-text mt-1">网游自主研发领域的前端的中国游戏公司</div>
                                  </div>

                              </div>
                              <div class="case-rate ">
                                    <div class="rate-item mr-3">
                                        <div class="rate-num d-flex align-items-center">
                                            <span class="number" data-target="24"></span>
                                                <div class="ml-1 text-center num-info">
                                                    <img class="up-down roat-deg" src="/static/libraries_v4/view/home/<USER>/down.svg" />
                                                    <div class="fn18 color-default">%</div>
                                                </div>
                                            </div>
                                        <div class="rate-name fn12">用户留存率</div>
                                    </div>
                                    <div class="rate-item mr-2">
                                        <div class="rate-num d-flex align-items-center">
                                            <span class="number" data-target="5"></span>
                                                <div class="ml-1 text-center num-info">
                                                    <img class="up-down roat-deg" src="/static/libraries_v4/view/home/<USER>/down.svg" />
                                                    <div class="fn18 color-default">s</div>
                                                </div>
                                            </div>
                                        <div class="rate-name fn12">响应速度</div>
                                    </div>
                              </div>
                          </div>
                          <p class="about-title mt-4 mb-0">相关产品</p>
                          <ul class="about-list"></ul>
                      `
                    },
                    {
                        "title": "社区论坛",
                        "icon": "/static/libraries_v4/view/home/<USER>/homeicon/game2.png",

                        "mobile": "/static/libraries_v4/view/home/<USER>/homeicon/nobiao.png",
                        "mb_news": "/static/libraries_v4/view/home/<USER>/game/img2.png",
                        "new_text1": "聚焦社区生态",
                        "new_text2": "增强短信",
                        "news_text_index1": 'img-txt-style14',
                        "news_text_index2": 'img-txt-style15',

                        "content": `
                          <h5 class="sub-nav-title">社区论坛</h5>
                          <p class="sub-nav-text mt-3">向用户发送最新通知，确保用户能够及时获取相关信息；鼓励用户之间进行互动，增强社区的社交性和互动性，吸引更多玩家关注，促进社区论坛的运营。</p>
                          <div class="case mt-3">
                              <div class="case-left">
                                    <div class="case-logo mr-3 ml-2">
                                        <img src="/static/libraries_v4/view/home/<USER>/partner/logo/partner_33.png" alt=""/>
                                    </div>
                                  <div class="case-info">
                                        <a href="/solugame"><div class="case-name d-flex align-items-center">小黑盒 <span class="ml-1 icon iconfont4 icon-jiantou_1 roda-arrow"></div> </a>
                                        <div class="case-text mt-1">为Steam游戏玩家定制的高能玩家聚集地</div>
                                  </div>

                              </div>
                              <div class="case-rate ">
                                    <div class="rate-item mr-3">
                                        <div class="rate-num d-flex align-items-center">
                                            <span class="number" data-target="30"></span>
                                                <div class="ml-1 text-center num-info">
                                                    <img class="up-down roat-deg" src="/static/libraries_v4/view/home/<USER>/down.svg" />
                                                    <div class="fn18 color-default">%</div>
                                                </div>
                                            </div>
                                        <div class="rate-name fn12">通知速度</div>
                                    </div>
                                    <div class="rate-item mr-2">
                                        <div class="rate-num d-flex align-items-center">
                                            <span class="number" data-target="28"></span>
                                                <div class="ml-1 text-center num-info">
                                                    <img class="up-down " src="/static/libraries_v4/view/home/<USER>/down.svg" />
                                                    <div class="fn18 color-default">%</div>
                                                </div>
                                            </div>
                                        <div class="rate-name fn12">运营成本</div>
                                    </div>
                              </div>
                          </div>
                          <p class="about-title mt-4 mb-0">相关产品</p>
                          <ul class="about-list"></ul>
                      `
                    },
                    {
                        "title": "交易平台",
                        "icon": "/static/libraries_v4/view/home/<USER>/homeicon/game3.png",

                        "mobile": "/static/libraries_v4/view/home/<USER>/homeicon/nobiao.png",
                        "mb_news": "/static/libraries_v4/view/home/<USER>/game/img3.png",
                        "new_text1": "完善账号安全保护机制",
                        "new_text2": "上行回复",
                        "news_text_index1": 'img-txt-style16',
                        "news_text_index2": 'img-txt-style17',
                        "class_name": 'jiaoyi-img',

                        "content": `
                          <h5 class="sub-nav-title">交易平台</h5>
                          <p class="sub-nav-text mt-3">利用好云通信服务可以增加平台收入，为玩家提供更优质的服务和体验。同时也有助于提高平台的知名度和竞争力，吸引更多的玩家和用户。</p>
                          <div class="case mt-3">
                              <div class="case-left">
                                    <div class="case-logo mr-3 ml-2">
                                        <img src="/static/libraries_v4/view/home/<USER>/section_4/logo/tx.png" alt=""/>
                                    </div>
                                  <div class="case-info">
                                        <a href="/solugame"><div class="case-name d-flex align-items-center">腾讯游戏 <span class="ml-1 icon iconfont4 icon-jiantou_1 roda-arrow"></div> </a>
                                        <div class="case-text mt-1">致力于为用户创造高品质数字游戏生活体验</div>
                                  </div>

                              </div>
                              <div class="case-rate ">
                                    <div class="rate-item mr-3">
                                        <div class="rate-num d-flex align-items-center">
                                            <span class="number" data-target="19"></span>
                                                <div class="ml-1 text-center num-info">
                                                    <img class="up-down roat-deg" src="/static/libraries_v4/view/home/<USER>/down.svg" />
                                                    <div class="fn18 color-default">%</div>
                                                </div>
                                            </div>
                                        <div class="rate-name fn12">平台竞争力</div>
                                    </div>
                                    <div class="rate-item mr-2">
                                        <div class="rate-num d-flex align-items-center">
                                            <span class="number" data-target="40"></span>
                                                <div class="ml-1 text-center num-info">
                                                    <img class="up-down roat-deg" src="/static/libraries_v4/view/home/<USER>/down.svg" />
                                                    <div class="fn18 color-default">%</div>
                                                </div>
                                            </div>
                                        <div class="rate-name fn12">玩家参与度</div>
                                    </div>
                              </div>
                          </div>
                          <p class="about-title mt-4 mb-0">相关产品</p>
                          <ul class="about-list"></ul>
                      `
                    },
                ]
            },

            {
                "title": "制造行业",
                "children": [
                    {
                        "title": "汽车出行",
                        "icon": "/static/libraries_v4/view/home/<USER>/homeicon/car_1.png",
                        "mobile": "/static/libraries_v4/view/home/<USER>/section_4/1_1.png",
                        "content": `
                          <h5 class="sub-nav-title">汽车出行</h5>
                          <p class="sub-nav-text mt-3">通过云通信平台，打造综合性的校园通信管理平台。提升教育机构的竞争力、传播力和影响力，提升学生和家长的用户体验和认同感。</p>
                          <div class="case mt-3">
                              <div class="case-left">
                                  <div class="case-logo mr-3 ml-2">
                                      <img src="/static/libraries_v4/view/home/<USER>/section_4/logo/shdx.svg" alt=""/>
                                  </div>
                                  <div class="case-info">
                                      <div class="case-name">上海大学</div>
                                      <div class="case-text mt-1">国家“211工程”重点建设的综合性大学</div>
                                  </div>
                              </div>
                              <div class="case-rate ">
                                  <div class="rate-item">
                                      <div class="rate-num">
                                          <span>3s</span>
                                          <span class="shang-wrap"><img src="/static/libraries_v4/view/home/<USER>/section_4/shang.svg" alt=""/></span>
                                      </div>
                                      <div class="rate-name">发送速度</div>
                                  </div>
                                  <div class="rate-item mr-3">
                                      <div class="rate-num">
                                          <span>18%</span>
                                          <span class="shang-wrap"><img src="/static/libraries_v4/view/home/<USER>/section_4/shang.svg" alt=""/></span>
                                      </div>
                                      <div class="rate-name">保存率</div>
                                  </div>
                              </div>
                          </div>
                          <p class="about-title mt-4 mb-0">相关产品</p>
                          <ul class="about-list">
                              <li class="about-item mr-3 mt-3 animated fadeIn" style="animation-delay: 0ms;">短信服务</li>
                              <li class="about-item mr-3 mt-3 animated fadeIn" style="animation-delay: 50ms;">一键登录</li>
                              <li class="about-item mr-3 mt-3 animated fadeIn" style="animation-delay: 100ms;">5G 消息</li>
                          </ul>
                      `
                    },
                    {
                        "title": "交通物流",
                        "icon": "/static/libraries_v4/view/home/<USER>/homeicon/car_2.png",
                        "mobile": "/static/libraries_v4/view/home/<USER>/section_4/1_1.png",
                        "content": `
                                  <h5 class="sub-nav-title">交通物流</h5>
                                  <p class="sub-nav-text mt-3">通过云通信平台，打造综合性的校园通信管理平台。提升教育机构的竞争力、传播力和影响力，提升学生和家长的用户体验和认同感。</p>
                                  <div class="case mt-3">
                                      <div class="case-left">
                                          <div class="case-logo mr-3 ml-2">
                                      <img src="/static/libraries_v4/view/home/<USER>/section_4/logo/shdx.svg" alt=""/>
                                  </div>
                                          <div class="case-info">
                                              <div class="case-name">上海大学</div>
                                              <div class="case-text mt-1">国家“211工程”重点建设的综合性大学</div>
                                          </div>
                                      </div>
                                      <div class="case-rate ">
                                          <div class="rate-item">
                                              <div class="rate-num">
                                                  <span>3s</span>
                                          <span class="shang-wrap"><img src="/static/libraries_v4/view/home/<USER>/section_4/shang.svg" alt=""/></span>
                                              </div>
                                              <div class="rate-name">发送速度</div>
                                          </div>
                                          <div class="rate-item mr-3">
                                              <div class="rate-num">
                                                  <span>18%</span>
                                          <span class="shang-wrap"><img src="/static/libraries_v4/view/home/<USER>/section_4/shang.svg" alt=""/></span>
                                              </div>
                                              <div class="rate-name">保存率</div>
                                          </div>
                                      </div>
                                  </div>
                                  <p class="about-title mt-4 mb-0">相关产品</p>
                                  <ul class="about-list">
                                      <li class="about-item mr-3 mt-3 animated fadeIn" style="animation-delay: 0ms;">短信服务</li>
                                      <li class="about-item mr-3 mt-3 animated fadeIn" style="animation-delay: 50ms;">一键登录</li>
                                      <li class="about-item mr-3 mt-3 animated fadeIn" style="animation-delay: 100ms;">5G 消息</li>
                                  </ul>
                              `
                    },
                    {
                        "title": "家具家电",
                        "icon": "/static/libraries_v4/view/home/<USER>/homeicon/car_3.png",
                        "mobile": "/static/libraries_v4/view/home/<USER>/section_4/1_1.png",
                        "content": `
                                  <h5 class="sub-nav-title">家具家电</h5>
                                  <p class="sub-nav-text mt-3">通过云通信平台，打造综合性的校园通信管理平台。提升教育机构的竞争力、传播力和影响力，提升学生和家长的用户体验和认同感。</p>
                                  <div class="case mt-3">
                                      <div class="case-left">
                                          <div class="case-logo mr-3 ml-2">
                                      <img src="/static/libraries_v4/view/home/<USER>/section_4/logo/shdx.svg" alt=""/>
                                  </div>
                                          <div class="case-info">
                                              <div class="case-name">上海大学</div>
                                              <div class="case-text mt-1">国家“211工程”重点建设的综合性大学</div>
                                          </div>
                                      </div>
                                      <div class="case-rate ">
                                          <div class="rate-item">
                                              <div class="rate-num">
                                                  <span>3s</span>
                                          <span class="shang-wrap"><img src="/static/libraries_v4/view/home/<USER>/section_4/shang.svg" alt=""/></span>
                                              </div>
                                              <div class="rate-name">发送速度</div>
                                          </div>
                                          <div class="rate-item mr-3">
                                              <div class="rate-num">
                                                  <span>18%</span>
                                          <span class="shang-wrap"><img src="/static/libraries_v4/view/home/<USER>/section_4/shang.svg" alt=""/></span>
                                              </div>
                                              <div class="rate-name">保存率</div>
                                          </div>
                                      </div>
                                  </div>
                                  <p class="about-title mt-4 mb-0">相关产品</p>
                                  <ul class="about-list">
                                      <li class="about-item mr-3 mt-3 animated fadeIn" style="animation-delay: 0ms;">短信服务</li>
                                      <li class="about-item mr-3 mt-3 animated fadeIn" style="animation-delay: 50ms;">一键登录</li>
                                      <li class="about-item mr-3 mt-3 animated fadeIn" style="animation-delay: 100ms;">5G 消息</li>
                                  </ul>
                              `
                    }
                ]
            },
            {
                "title": "通用行业",
                "children": [
                    {
                        "title": "数字营销",
                        "icon": "/static/libraries_v4/view/home/<USER>/homeicon/common_1.png",
                        "mobile": "/static/libraries_v4/view/home/<USER>/section_4/1_1.png",
                        "content": `
                          <h5 class="sub-nav-title">数字营销</h5>
                          <p class="sub-nav-text mt-3">通过云通信平台，打造综合性的校园通信管理平台。提升教育机构的竞争力、传播力和影响力，提升学生和家长的用户体验和认同感。</p>
                          <div class="case mt-3">
                              <div class="case-left">
                                  <div class="case-logo mr-3 ml-2">
                                      <img src="/static/libraries_v4/view/home/<USER>/section_4/logo/shdx.svg" alt=""/>
                                  </div>
                                  <div class="case-info">
                                      <div class="case-name">上海大学</div>
                                      <div class="case-text mt-1">国家“211工程”重点建设的综合性大学</div>
                                  </div>
                              </div>
                              <div class="case-rate ">
                                  <div class="rate-item">
                                      <div class="rate-num">
                                          <span>3s</span>
                                          <span class="shang-wrap"><img src="/static/libraries_v4/view/home/<USER>/section_4/shang.svg" alt=""/></span>
                                      </div>
                                      <div class="rate-name">发送速度</div>
                                  </div>
                                  <div class="rate-item mr-3">
                                      <div class="rate-num">
                                          <span>18%</span>
                                          <span class="shang-wrap"><img src="/static/libraries_v4/view/home/<USER>/section_4/shang.svg" alt=""/></span>
                                      </div>
                                      <div class="rate-name">保存率</div>
                                  </div>
                              </div>
                          </div>
                          <p class="about-title mt-4 mb-0">相关产品</p>
                          <ul class="about-list">
                              <li class="about-item mr-3 mt-3 animated fadeIn" style="animation-delay: 0ms;">短信服务</li>
                              <li class="about-item mr-3 mt-3 animated fadeIn" style="animation-delay: 50ms;">一键登录</li>
                              <li class="about-item mr-3 mt-3 animated fadeIn" style="animation-delay: 100ms;">5G 消息</li>
                          </ul>
                      `
                    },
                    {
                        "title": "系统通知",
                        "icon": "/static/libraries_v4/view/home/<USER>/homeicon/common_2.png",
                        "mobile": "/static/libraries_v4/view/home/<USER>/section_4/1_1.png",
                        "content": `
                                  <h5 class="sub-nav-title">系统通知</h5>
                                  <p class="sub-nav-text mt-3">通过云通信平台，打造综合性的校园通信管理平台。提升教育机构的竞争力、传播力和影响力，提升学生和家长的用户体验和认同感。</p>
                                  <div class="case mt-3">
                                      <div class="case-left">
                                          <div class="case-logo mr-3 ml-2">
                                      <img src="/static/libraries_v4/view/home/<USER>/section_4/logo/shdx.svg" alt=""/>
                                  </div>
                                          <div class="case-info">
                                              <div class="case-name">上海大学</div>
                                              <div class="case-text mt-1">国家“211工程”重点建设的综合性大学</div>
                                          </div>
                                      </div>
                                      <div class="case-rate ">
                                          <div class="rate-item">
                                              <div class="rate-num">
                                                  <span>3s</span>
                                          <span class="shang-wrap"><img src="/static/libraries_v4/view/home/<USER>/section_4/shang.svg" alt=""/></span>
                                              </div>
                                              <div class="rate-name">发送速度</div>
                                          </div>
                                          <div class="rate-item mr-3">
                                              <div class="rate-num">
                                                  <span>18%</span>
                                          <span class="shang-wrap"><img src="/static/libraries_v4/view/home/<USER>/section_4/shang.svg" alt=""/></span>
                                              </div>
                                              <div class="rate-name">保存率</div>
                                          </div>
                                      </div>
                                  </div>
                                  <p class="about-title mt-4 mb-0">相关产品</p>
                                  <ul class="about-list">
                                      <li class="about-item mr-3 mt-3 animated fadeIn" style="animation-delay: 0ms;">短信服务</li>
                                      <li class="about-item mr-3 mt-3 animated fadeIn" style="animation-delay: 50ms;">一键登录</li>
                                      <li class="about-item mr-3 mt-3 animated fadeIn" style="animation-delay: 100ms;">5G 消息</li>
                                  </ul>
                              `
                    },
                    {
                        "title": "企业出海",
                        "icon": "/static/libraries_v4/view/home/<USER>/homeicon/common_3.png",
                        "mobile": "/static/libraries_v4/view/home/<USER>/section_4/1_1.png",
                        "content": `
                                  <h5 class="sub-nav-title">企业出海</h5>
                                  <p class="sub-nav-text mt-3">通过云通信平台，打造综合性的校园通信管理平台。提升教育机构的竞争力、传播力和影响力，提升学生和家长的用户体验和认同感。</p>
                                  <div class="case mt-3">
                                      <div class="case-left">
                                          <div class="case-logo mr-3 ml-2">
                                      <img src="/static/libraries_v4/view/home/<USER>/section_4/logo/shdx.svg" alt=""/>
                                  </div>
                                          <div class="case-info">
                                              <div class="case-name">上海大学</div>
                                              <div class="case-text mt-1">国家“211工程”重点建设的综合性大学</div>
                                          </div>
                                      </div>
                                      <div class="case-rate ">
                                          <div class="rate-item">
                                              <div class="rate-num">
                                                  <span>3s</span>
                                          <span class="shang-wrap"><img src="/static/libraries_v4/view/home/<USER>/section_4/shang.svg" alt=""/></span>
                                              </div>
                                              <div class="rate-name">发送速度</div>
                                          </div>
                                          <div class="rate-item mr-3">
                                              <div class="rate-num">
                                                  <span>18%</span>
                                          <span class="shang-wrap"><img src="/static/libraries_v4/view/home/<USER>/section_4/shang.svg" alt=""/></span>
                                              </div>
                                              <div class="rate-name">保存率</div>
                                          </div>
                                      </div>
                                  </div>
                                  <p class="about-title mt-4 mb-0">相关产品</p>
                                  <ul class="about-list">
                                      <li class="about-item mr-3 mt-3 animated fadeIn" style="animation-delay: 0ms;">短信服务</li>
                                      <li class="about-item mr-3 mt-3 animated fadeIn" style="animation-delay: 50ms;">一键登录</li>
                                      <li class="about-item mr-3 mt-3 animated fadeIn" style="animation-delay: 100ms;">5G 消息</li>
                                  </ul>
                              `
                    }
                ]
            },
        ],
        init: function () {
            var _this = this;
            this.initNav();
            this.setNavIndex(0);
            $('.section-4 .nav-item').on('click', function () {

                var index = $(this).data('index');
                if (_this.activeNavIndex == index) return;
                _this.setNavIndex(index);
            });

        },
        initNav: function () {
            var ul_nav = '';
            this.list.forEach(function (item, index) {
                ul_nav += `<li class="nav-item" data-index="${index}">${item.title}</li>`;
            });
            $('.section-4 ul.nav-top').empty().append(ul_nav);
        },
        initSubNav: function () {
            var _this = this;
            var ul_sub_nav = '';
            this.list[this.activeNavIndex].children.forEach(function (item, index) {
                ul_sub_nav += `
                  <li class="sub-nav-item" data-index="${index}">
                      <img src="${item.icon}" alt="">
                      <span class="mt-1">${item.title}</span>
                  </li>
              `;
            });
            $('.section-4 ul.sub-nav').empty().append(ul_sub_nav);
            $('.section-4 .sub-nav-item').on('click', function () {
                var index = $(this).data('index');
                if (_this.activeSubNavIndex == index) return;
                _this.setSubNavIndex(index);
            });
        },
        setNavIndex: function (index) {

            $('.section-4 .nav-content .bg img').eq(index).show().siblings().hide();

            this.activeNavIndex = index;
            this.initSubNav();
            this.setSubNavIndex(0);
            $('.section-4 ul.nav-top').children().each(function () {
                $(this).removeClass("active");
            }).eq(index).addClass("active");
            nav.randomPro();
        },
        setSubNavIndex: function (index) {
            this.activeSubNavIndex = index;
            $('.section-4 ul.sub-nav').children().each(function () {
                $(this).removeClass("active");
            }).eq(index).addClass("active");
            $('.section-4 .sub-nav-content').empty().append(this.list[this.activeNavIndex].children[index].content);
            this.setMobile();
            nav.randomPro();
            nav.numAn();

        },

        randomPro: function () {
            function getRandomArrayElements(arr, count) {
                var shuffled = arr.slice(0), i = arr.length, min = i - count, temp, index;
                while (i-- > min) {
                    index = Math.floor((i + 1) * Math.random());
                    temp = shuffled[index];
                    shuffled[index] = shuffled[i];
                    shuffled[i] = temp;
                }
                return shuffled.slice(min);
            }

            let items = [
                {
                    title: '短信',
                    href: '/sms'
                },
                {
                    title: '邮件',
                    href: '/mail'
                },
                {
                    title: '多媒体彩信',
                    href: '/mms'
                },
                {
                    title: '语音',
                    href: '/voice'
                },
                {
                    title: '短网址',
                    href: '/shorturl'
                },
                {
                    title: '国际短信',
                    href: '/internationalsms'
                },
                {
                    title: '智慧短信',
                    href: '/smartsms'
                },
                {
                    title: '免密登录',
                    href: '/onepass'
                },
                {
                    title: '身份验证',
                    href: '/factor'
                },
                {
                    title: '5G 阅信',
                    href: '/rcs'
                },
            ];
            var da = "";
            $.each(getRandomArrayElements(items, 3), function (i, d) {
                da += `<a href="${d.href}" class="mr-3 color-default"><li class="about-item fnbold mt-3 animated fadeIn" style="animation-delay: 0ms;">${d.title}</li> </a>`;
            });
            $('.section-4 ul.about-list').html(da);
        },
        // 数字动画
        numAn: function () {
            // 获取所有的dom,querySelectorAll为为数组
            const numbers = document.querySelectorAll(".number");
            numbers.forEach((item) => {
                item.innerText = 0;
                var lastNumber = Number(item.getAttribute("data-target"));
                nav.numberRock({
                    el: item,
                    lastNumber: lastNumber,
                    duration: lastNumber > 10 ? 800 : 80,
                    easing: 'linear',  //慢快慢
                });
            });
        },
        numberRock: function (options) {
            var defaults = {
                el: null,
                lastNumber: 0,
                duration: 800,
                easing: 'linear'  //swing(默认 : 缓冲 : 慢快慢)  linear(匀速的)
            };
            var opts = $.extend({}, defaults, options);

            $(opts.el).animate({
                num: "numberRock",
            }, {
                duration: opts.duration,
                easing: opts.easing,
                complete: function () {
                    // console.log("success");
                },
                step: function (a, b) {  //可以检测我们定时器的每一次变化
                    $(opts.el).html(parseInt(b.pos * opts.lastNumber));
                }
            });

        },
        setMobile: function () {
            var _img = `
              <img src="${this.list[this.activeNavIndex].children[this.activeSubNavIndex].mobile}" class="animated myzoomIn" alt="" />
          `;
            $(".section-4 .content-img").empty().append(_img);
            let _htmlNews = `
                <div class="phone-mode-img">
                    <div class="img-txt ${this.list[this.activeNavIndex].children[this.activeSubNavIndex].news_text_index1}">
                        <li class="li-disc">${this.list[this.activeNavIndex].children[this.activeSubNavIndex].new_text1}</li>
                    </div>
                    <img class="news-img1 ${this.list[this.activeNavIndex].children[this.activeSubNavIndex].class_name}" src="${this.list[this.activeNavIndex].children[this.activeSubNavIndex].mb_news}" alt="">
                    <div class="img-txt ${this.list[this.activeNavIndex].children[this.activeSubNavIndex].news_text_index2}">
                    <li class="li-disc">${this.list[this.activeNavIndex].children[this.activeSubNavIndex].new_text2}</li>
                </div>
                </div>
             `;
            $(".section-4 .phone-mode").empty().html(_htmlNews);
        }
    };
    var s7_swiper = {
        init: function () {
            new Swiper('.section-7 .swiper-container', {
                direction: 'vertical',
                loop: true,
                autoplay: 5000,
                // simulateTouch: false
            });
        }
    };
    var wufen = {
        list:
            [
                {
                    imgSrc: '/static/libraries_v4/view/home/<USER>/partner/logo/partner_1.png',
                    title: '京东商城',
                    tips: '综合购物平台',
                    desc: '中国电子商务领域受消费者欢迎和具有影响力的电子商务网站。'
                },
                {
                    imgSrc: '/static/libraries_v4/view/home/<USER>/partner/logo/partner_2.png',
                    title: '网易游戏',
                    tips: '游戏热爱者',
                    desc: '作为中国领先的游戏公司，一直处于网游自主研发领域的前端。'
                },
                {
                    imgSrc: '/static/libraries_v4/view/home/<USER>/partner/logo/partner_4.png',
                    title: '慕课网',
                    tips: '程序员的梦工厂',
                    desc: '国内领先的 IT 技能在线学习平台，帮助开发者实现职梦想。'
                },
                {
                    imgSrc: '/static/libraries_v4/view/home/<USER>/partner/logo/partner_5.png',
                    title: '米哈游',
                    tips: '技术宅拯救世界',
                    desc: '致力于为用户提供美好的、超预期的游戏/动画/音乐等多元产品。'
                },

                {
                    imgSrc: '/static/libraries_v4/view/home/<USER>/partner/logo/partner_7.png',
                    title: '大众点评',
                    tips: '发现好去处',
                    desc: '中国领先的本地生活信息及交易平台和独立第三方消费点评网站。'
                },
                {
                    imgSrc: '/static/libraries_v4/view/home/<USER>/partner/logo/partner_8.png',
                    title: '人民公安报',
                    tips: '综合购物平台',
                    desc: '坚持“立足公安、面向社会”方针的中华人民共和国公安部机关报。'
                },
                {
                    imgSrc: '/static/libraries_v4/view/home/<USER>/partner/logo/partner_9.png',
                    title: '有赞',
                    tips: 'SaaS服务提供商',
                    desc: '提供全行业经营增长解决方案，帮助商家全渠道私域营销运营。'
                },
                {
                    imgSrc: '/static/libraries_v4/view/home/<USER>/partner/logo/partner_10.png',
                    title: '上海大学',
                    tips: '211高校',
                    desc: '上海市属的“211 工程”重点建设高校、国家“双一流”建设高校。'
                },
                {
                    imgSrc: '/static/libraries_v4/view/home/<USER>/partner/logo/partner_11.png',
                    title: '网鱼网咖',
                    tips: '网咖领导者',
                    desc: '致力打造品牌化连锁网咖，成为多功能教育、娱乐的公众场所。'
                },
                {
                    imgSrc: '/static/libraries_v4/view/home/<USER>/partner/logo/partner_12.png',
                    title: '英孚教育',
                    tips: '知名教育机构',
                    desc: '致力于提供语言学习、留学旅游等各种课程的全球知名教育机构。'
                },
                {
                    imgSrc: '/static/libraries_v4/view/home/<USER>/partner/logo/partner_13.png',
                    title: '收钱吧',
                    tips: '生意好，收钱吧',
                    desc: '提供商户专业移动支付收款等多种服务，助力商户数字化经营。'
                },
                {
                    imgSrc: '/static/libraries_v4/view/home/<USER>/partner/logo/partner_14.png',
                    title: '复旦大学',
                    tips: '985高校',
                    desc: '位列国家“双一流”、“985工程”、“211工程”的综合性高校。'
                },
                {
                    imgSrc: '/static/libraries_v4/view/home/<USER>/partner/logo/partner_15.png',
                    title: '广州地铁',
                    tips: '国际地铁联盟成员',
                    desc: '提供从出行到商住的一体化、便捷化、人性化的轨道交通服务。'
                },
                {
                    imgSrc: '/static/libraries_v4/view/home/<USER>/partner/logo/partner_16.png',
                    title: '巨丰代运',
                    tips: '专业网购代运',
                    desc: '专业从事中国到马、新等亚洲其他地区代运业务的国际转运公司。'
                },
                {
                    imgSrc: '/static/libraries_v4/view/home/<USER>/partner/logo/partner_17.png',
                    title: '积目',
                    tips: '垂直社交平台',
                    desc: '去除社交APP应有的固有模式，打造青年文化领域陌生人社交平台。'
                },
                {
                    imgSrc: '/static/libraries_v4/view/home/<USER>/partner/logo/partner_18.png',
                    title: '凯撒文化',
                    tips: '泛娱乐一体化运营商',
                    desc: '以精品IP为核心，从事文化产业出品投资的中国泛娱乐一体化公司。'
                },
                {
                    imgSrc: '/static/libraries_v4/view/home/<USER>/partner/logo/partner_19.png',
                    title: '龙湖',
                    tips: '知名地产集团',
                    desc: '涵盖地产开发、商业投资等多航道业务，实现全国重要城市布局。'
                },
                {
                    imgSrc: '/static/libraries_v4/view/home/<USER>/partner/logo/partner_21.png',
                    title: '威海商业银行',
                    tips: '十佳城市商业银行',
                    desc: '坚持“立足山东，精耕细作，特色发展”定位的股份制商业银行。'
                },
                {
                    imgSrc: '/static/libraries_v4/view/home/<USER>/partner/logo/partner_23.png',
                    title: '波克城市',
                    tips: 'BE WILD. BE FREE.',
                    desc: '立足精品休闲游戏全球化，致力成为世界领先的数字娱乐平台。'
                },
                {
                    imgSrc: '/static/libraries_v4/view/home/<USER>/partner/logo/partner_24.png',
                    title: 'Pingpong',
                    tips: '全球支付平台',
                    desc: '为中国跨境卖家提供低成本的跨境收款以及定制服务的金融平台。'
                },
                {
                    imgSrc: '/static/libraries_v4/view/home/<USER>/partner/logo/partner_25.png',
                    title: 'Lazada',
                    tips: '在线跨境电商',
                    desc: '通过商业和科技促进市场发展，已成为东南亚人人都爱的电商平台。'
                },
                {
                    imgSrc: '/static/libraries_v4/view/home/<USER>/partner/logo/partner_26.png',
                    title: '易观分析',
                    tips: '激发科技与创新活力',
                    desc: '以海量数字用户资产及算法模型为核心的大数据分析工具和方案。'
                },
                {
                    imgSrc: '/static/libraries_v4/view/home/<USER>/partner/logo/partner_27.png',
                    title: 'API Cloud',
                    tips: '低代码开发平台',
                    desc: '致力于解放开发者，缩减开发时间，助力快速构建企业级应用。'
                },
                {
                    imgSrc: '/static/libraries_v4/view/home/<USER>/partner/logo/partner_28.png',
                    title: '腕表之家',
                    tips: '专业腕表平台',
                    desc: '提供最新手表品牌排名、报价信息，为您提供最具有价值的参考。'
                },
                {
                    imgSrc: '/static/libraries_v4/view/home/<USER>/partner/logo/partner_29.png',
                    title: '天马物流',
                    tips: '知名物流公司',
                    desc: '本着“客户，诚信至上”原则，与多家企业建立了长期合作关系。'
                },
                {
                    imgSrc: '/static/libraries_v4/view/home/<USER>/partner/logo/partner_30.png',
                    title: '贝壳',
                    tips: '新居住服务商',
                    desc: '致力于提供数字化和智能化等全方位、高品质的居住服务。'
                },
            ],
        init: function () {
            var _this = this;
            var lis = '';
            this.list.forEach(function (item, index) {
                var _html = `
                  <div class="section6-list-item">
                    <div class="section6-text">
                        <img src="${item.imgSrc}" alt="">
                    </div>
                    <div class="section6-name d-flex align-items-center">${item.title}  <div class="fn12 ml-2 section6-tips">${item.tips}</div> </div>
                    <div class="section6-desc">
                        <div>${item.desc}</div>
                    </div>
                </div>
              `;
                lis += _html;
            });
            $('.section-6 .section6-list').empty().append(lis);

        }
    };
    var shuju = {
        activeIndex: 0,
        timer: null,
        list: [
            {
                imgSrc: '/static/libraries_v4/view/home/<USER>/iso9004.png',
                dot: '三级等保',
                desc: '三级信息系统安全等级保护',
                desc_con: '信息系统安全等级保护三级认证是公安部用于指导国内各企业单位进行网络安全建设的依据，是对非银行机构的最高等级保护认证。'
            },
            {
                imgSrc: '/static/libraries_v4/view/home/<USER>/iso9002.png',
                dot: 'ISO 27001',
                desc: '信息安全管理体系认证',
                desc_con: 'ISO 27001是当今国际上最权威，也是最被广泛接受和应用的信息安全领域的体系认证标准，是企业核心竞争力的重要标志。'
            },
            {
                imgSrc: '/static/libraries_v4/view/home/<USER>/iso9003.png',
                dot: '高新技术企业',
                desc: '拥有核心自主知识产权的企业',
                desc_con: '高新技术企业是在国家重点支持领域内拥有核心自主知识产权并持续进行研究开发与转化的企业，SUBMAIL 连续多年获得认证。'
            },
            {
                imgSrc: '/static/libraries_v4/view/home/<USER>/iso9001.png',
                dot: 'ISO 9001',
                desc: '信息技术服务管理体系认证',
                desc_con: 'ISO 9001是由全球第一个质量管理体系标准，是迄今为止世界上最成熟的质量框架，是企业发展和成长之根本。'
            },

        ],
        init: function () {
            this.initDots();
            this.setActive(0);
        },
        initDots: function () {
            var _this = this;
            var lis = '';
            this.list.forEach(function (item, index) {
                var _li = `
                    <div class="col-sm-12 col-md-6 data-right-item " data-index="${index}">
                        <div class="right-card">
                            <img src="${item.imgSrc}" class=""/>
                            <div class="ml-2 r-item-title ">
                                <span class="mt-2 fn18 fnbold">${item.dot}</span>
                                <p class="mb-0 fn16 color-gray">${item.desc}</p>
                            </div>
                            <div class=" is-show-txt color-gray">${item.desc_con}</div>
                        </div>
                    </div>
              `;
                lis += _li;
            });
            $('.section-8 .data-right').empty().append(lis);
            $('.section-8 .data-right-item').on('click', function () {
                var index = $(this).data('index');
                if (index == _this.activeIndex) return;
                _this.setActive(index);
            });
        },
        setActive: function (index) {
            if (this.timer) clearTimeout(this.timer);
            var _this = this;

            $('.section-8 .data-right').children().each(function () {
                $(this).removeClass("active");
                $(this).find('img').removeClass('active');
                $(this).find('.r-item-title').removeClass('active');
                $(this).find('.is-show-txt').removeClass('active');
            }).eq(index).addClass("active").find('img,.r-item-title,.is-show-txt').addClass('active');

            this.activeIndex = index;
            this.timer = setTimeout(function () {
                if (_this.activeIndex == _this.list.length - 1) {
                    _this.setActive(0);
                } else {
                    _this.setActive(_this.activeIndex + 1);
                }
            }, 12000);
        }
    };

    var faqlist = [
        {
            title: '教育行业',
            children: [
                {
                    icon: '/static/libraries_v4/view/home/<USER>/homeicon/school_1.png',
                    q: '智慧校园',
                    a: `<div class="sub-nav-content" style="width:100%">
                            <h5 class="sub-nav-title">智慧校园</h5>
                            <p class="sub-nav-text mt-3">不同通知营销工具匹配多元智慧校园场景，促进学校/教师/家长/学生之间的沟通互动能力与管理能力，提升校园的数字化与智慧化程度，从而更好地实现教育目标。</p>
                            <div class="case mt-3">
                                <div class="case-left">
                                    <div class="case-logo mr-3 ml-2">
                                        <img src="/static/libraries_v4/view/home/<USER>/section_4/logo/fudan.png" alt="">
                                    </div>
                                    <div class="case-info">
                                        <a href="/education"><div class="case-name d-flex align-items-center">复旦大学 <span class="ml-1 icon iconfont4 icon-jiantou_1 roda-arrow"></span></div> </a>
                                        <div class="case-text mt-1">国家“双一流”、“985工程”的综合性高校</div>
                                    </div>

                                </div>
                                <div class="case-rate ">
                                    <div class="rate-item mr-3">
                                        <div class="rate-num d-flex align-items-center">
                                            <span class="number" data-target="30">30</span>
                                                <div class="ml-1 text-center num-info">
                                                    <img class="up-down roat-deg" src="/static/libraries_v4/view/home/<USER>/down.svg">
                                                    <div class="fn18 color-default">%</div>
                                                </div>
                                            </div>
                                        <div class="rate-name fn12">教育数智化</div>
                                    </div>
                                    <div class="rate-item mr-2">
                                        <div class="rate-num d-flex align-items-center">
                                            <span class="number" data-target="3">3</span>
                                                <div class="ml-1 text-center num-info">
                                                    <img class="up-down " src="/static/libraries_v4/view/home/<USER>/down.svg">
                                                    <div class="fn18 color-default">s</div>
                                                </div>
                                            </div>
                                        <div class="rate-name fn12">发送速度</div>
                                    </div>
                                </div>
                            </div>
                            <p class="about-title mt-4 mb-0">相关产品</p>
                            <ul class="about-list"></ul>
                        </div>`
                },
                {
                    icon: '/static/libraries_v4/view/home/<USER>/homeicon/school_3.png',
                    q: '在线教育',
                    a: `<div class="sub-nav-content" style="width:100%">
                            <h5 class="sub-nav-title">在线教育</h5>
                            <p class="sub-nav-text mt-3">云通信服务在保障用户学习环境和信息安全的同时，又可向学生发送课程预告、作业考试等信息，提高学生的学习效率和管理效率，是在线教育不可或缺的一部分。</p>
                            <div class="case mt-3">
                                <div class="case-left">
                                    <div class="case-logo mr-3 ml-2">
                                        <img src="/static/libraries_v4/view/home/<USER>/section_4/logo/muke.png" alt="">
                                    </div>
                                    <div class="case-info">
                                        <a href="/education"><div class="case-name d-flex align-items-center">慕课网 <span class="ml-1 icon iconfont4 icon-jiantou_1 roda-arrow"></span></div> </a>
                                        <div class="case-text mt-1">国内领先的 IT 技能在线学习平台</div>
                                    </div>

                                </div>
                                <div class="case-rate ">
                                    <div class="rate-item mr-3">
                                        <div class="rate-num d-flex align-items-center">
                                            <span class="number" data-target="28">28</span>
                                                <div class="ml-1 text-center num-info">
                                                    <img class="up-down roat-deg" src="/static/libraries_v4/view/home/<USER>/down.svg">
                                                    <div class="fn18 color-default">%</div>
                                                </div>
                                            </div>
                                        <div class="rate-name fn12">响应速度</div>
                                    </div>
                                    <div class="rate-item mr-2">
                                        <div class="rate-num d-flex align-items-center">
                                            <span class="number" data-target="40">40</span>
                                                <div class="ml-1 text-center num-info">
                                                    <img class="up-down roat-deg" src="/static/libraries_v4/view/home/<USER>/down.svg">
                                                    <div class="fn18 color-default">%</div>
                                                </div>
                                            </div>
                                        <div class="rate-name fn12">安全性</div>
                                    </div>
                                </div>
                            </div>
                            <p class="about-title mt-4 mb-0">相关产品</p>
                            <ul class="about-list"></ul>
                        </div>`
                },
                {
                    icon: '/static/libraries_v4/view/home/<USER>/homeicon/school_2.png',
                    q: 'k12教育',
                    a: `
                    <div class="sub-nav-content" style="width:100%">
                        <h5 class="sub-nav-title">k12教育</h5>
                        <p class="sub-nav-text mt-3">方便家长及时了解学校活动安排和孩子课程进度，促进家长和孩子之间的互动交流。在兼顾低成本和高效率的同时，增强对k12教育的了解和认可，提升招生入学率。</p>
                        <div class="case mt-3">
                            <div class="case-left">
                                <div class="case-logo mr-3 ml-2">
                                    <img src="/static/libraries_v4/view/home/<USER>/section_4/logo/yf.png" alt="">
                                </div>
                                <div class="case-info">
                                    <a href="/education"><div class="case-name d-flex align-items-center">英孚教育 <span class="ml-1 icon iconfont4 icon-jiantou_1 roda-arrow"></span></div> </a>
                                    <div class="case-text mt-1">提供语言学习等课程的全球知名教育机构</div>
                                </div>

                            </div>
                            <div class="case-rate ">
                                <div class="rate-item mr-3">
                                    <div class="rate-num d-flex align-items-center">
                                        <span class="number" data-target="12">12</span>
                                            <div class="ml-1 text-center num-info">
                                                <img class="up-down roat-deg" src="/static/libraries_v4/view/home/<USER>/down.svg">
                                                <div class="fn18 color-default">%</div>
                                            </div>
                                        </div>
                                    <div class="rate-name fn12">入学率</div>
                                </div>
                                <div class="rate-item mr-2">
                                    <div class="rate-num d-flex align-items-center">
                                        <span class="number" data-target="20">20</span>
                                            <div class="ml-1 text-center num-info">
                                                <img class="up-down" src="/static/libraries_v4/view/home/<USER>/down.svg">
                                                <div class="fn18 color-default">%</div>
                                            </div>
                                        </div>
                                    <div class="rate-name fn12">推广成本</div>
                                </div>
                            </div>
                        </div>
                        <p class="about-title mt-4 mb-0">相关产品</p>
                        <ul class="about-list"><a href="/mms" class="mr-3 color-default"><li class="about-item fnbold mt-3 animated fadeIn" style="animation-delay: 0ms;">多媒体彩信</li> </a><a href="/onepass" class="mr-3 color-default"><li class="about-item fnbold mt-3 animated fadeIn" style="animation-delay: 0ms;">免密登录</li> </a><a href="/voice" class="mr-3 color-default"><li class="about-item fnbold mt-3 animated fadeIn" style="animation-delay: 0ms;">语音</li> </a></ul>
                    </div>
              `
                },

            ]
        },
        {
            title: '电商行业',
            children: [
                {
                    icon: '/static/libraries_v4/view/home/<USER>/homeicon/dian1.png',
                    q: '平台电商',
                    a: `
                        <div class="sub-nav-content" style="width:100%">
                            <h5 class="sub-nav-title">平台电商</h5>
                            <p class="sub-nav-text mt-3">通过云通信服务及时解决客户的问题，提高客户对商家的满意度和忠诚度，可有效提升平台电商的用户体验和运营效率，同时也为电商平台提供了信息安全保障。</p>
                            <div class="case mt-3">
                                <div class="case-left">
                                    <div class="case-logo mr-3 ml-2">
                                        <img src="/static/libraries_v4/view/home/<USER>/section_4/logo/jd.png" alt="">
                                    </div>
                                    <div class="case-info">
                                        <a href="/solution"><div class="case-name d-flex align-items-center">京东商城 <span class="ml-1 icon iconfont4 icon-jiantou_1 roda-arrow"></span></div> </a>
                                        <div class="case-text mt-1">电子商务领域受消费者欢迎的综合网购平台</div>
                                    </div>

                                </div>
                                <div class="case-rate ">
                                    <div class="rate-item mr-3">
                                        <div class="rate-num d-flex align-items-center">
                                            <span class="number" data-target="20">20</span>
                                                <div class="ml-1 text-center num-info">
                                                    <img class="up-down roat-deg" src="/static/libraries_v4/view/home/<USER>/down.svg">
                                                    <div class="fn18 color-default">%</div>
                                                </div>
                                            </div>
                                        <div class="rate-name fn12">复购率</div>
                                    </div>
                                    <div class="rate-item mr-2">
                                        <div class="rate-num d-flex align-items-center">
                                            <span class="number" data-target="30">30</span>
                                                <div class="ml-1 text-center num-info">
                                                    <img class="up-down " src="/static/libraries_v4/view/home/<USER>/down.svg">
                                                    <div class="fn18 color-default">%</div>
                                                </div>
                                            </div>
                                        <div class="rate-name fn12">推广成本</div>
                                    </div>
                                </div>
                            </div>
                            <p class="about-title mt-4 mb-0">相关产品</p>
                            <ul class="about-list">
                            </ul>
                        </div>
              `
                },
                {
                    icon: '/static/libraries_v4/view/home/<USER>/homeicon/dian2.png',
                    q: '跨境电商',
                    a: `
                    <div class="sub-nav-content" style="width:100%">
                        <h5 class="sub-nav-title">跨境电商</h5>
                        <p class="sub-nav-text mt-3">拓展海外市场和提高品牌知名度是跨境电商的核心所在。通过云通信平台，可提升目标受众集中度，快速吸引更多潜在客户，全方位提高曝光率、影响力和实际效率。</p>
                        <div class="case mt-3">
                            <div class="case-left">
                                <div class="case-logo mr-3 ml-2">
                                    <img src="/static/libraries_v4/view/home/<USER>/section_4/logo/la.png" alt="">
                                </div>
                                <div class="case-info">
                                    <a href="/solution"><div class="case-name d-flex align-items-center">Lazada <span class="ml-1 icon iconfont4 icon-jiantou_1 roda-arrow"></span></div> </a>
                                    <div class="case-text mt-1">东南亚区域人人爱用的在线跨境电商平台</div>
                                </div>

                            </div>
                            <div class="case-rate ">
                                <div class="rate-item mr-3">
                                    <div class="rate-num d-flex align-items-center">
                                        <span class="number" data-target="30">30</span>
                                            <div class="ml-1 text-center num-info">
                                                <img class="up-down roat-deg" src="/static/libraries_v4/view/home/<USER>/down.svg">
                                                <div class="fn18 color-default">s</div>
                                            </div>
                                        </div>
                                    <div class="rate-name fn12">品牌知名度</div>
                                </div>
                                <div class="rate-item mr-2">
                                    <div class="rate-num d-flex align-items-center">
                                        <span class="number" data-target="50">50</span>
                                            <div class="ml-1 text-center num-info">
                                                <img class="up-down roat-deg" src="/static/libraries_v4/view/home/<USER>/down.svg">
                                                <div class="fn18 color-default">%</div>
                                            </div>
                                        </div>
                                    <div class="rate-name fn12">目标受众集中度</div>
                                </div>
                            </div>
                        </div>
                        <p class="about-title mt-4 mb-0">相关产品</p>
                        <ul class="about-list"></ul>
                    </div>`
                },
                {
                    icon: '/static/libraries_v4/view/home/<USER>/homeicon/dian3.png',
                    q: '新零售 +',
                    a: `
                    <div class="sub-nav-content" style="width:100%">
                        <h5 class="sub-nav-title">新零售 +</h5>
                        <p class="sub-nav-text mt-3">助力商家拓展客群，协助商家解决在移动互联网时代遇到的推广获客、成交转化、复购增长、分享裂变等问题，实现渠道数字化、智能化和国际化。</p>
                        <div class="case mt-3">
                            <div class="case-left">
                                <div class="case-logo mr-3 ml-2">
                                    <img src="/static/libraries_v4/view/home/<USER>/section_4/logo/yz.png" alt="">
                                </div>
                                <div class="case-info">
                                    <a href="/solution"><div class="case-name d-flex align-items-center">有赞 <span class="ml-1 icon iconfont4 icon-jiantou_1 roda-arrow"></span></div> </a>
                                    <div class="case-text mt-1">提供全行业解决方案的 SaaS 服务提供商</div>
                                </div>

                            </div>
                            <div class="case-rate ">
                                <div class="rate-item mr-3">
                                    <div class="rate-num d-flex align-items-center">
                                        <span class="number" data-target="20">20</span>
                                            <div class="ml-1 text-center num-info">
                                                <img class="up-down " src="/static/libraries_v4/view/home/<USER>/down.svg">
                                                <div class="fn18 color-default">%</div>
                                            </div>
                                        </div>
                                    <div class="rate-name fn12">推广成本</div>
                                </div>
                                <div class="rate-item mr-2">
                                    <div class="rate-num d-flex align-items-center">
                                        <span class="number" data-target="30">30</span>
                                            <div class="ml-1 text-center num-info">
                                                <img class="up-down roat-deg" src="/static/libraries_v4/view/home/<USER>/down.svg">
                                                <div class="fn18 color-default">%</div>
                                            </div>
                                        </div>
                                    <div class="rate-name fn12">拓客速度</div>
                                </div>
                            </div>
                        </div>
                        <p class="about-title mt-4 mb-0">相关产品</p>
                        <ul class="about-list"><a href="/smartsms" class="mr-3 color-default"><li class="about-item fnbold mt-3 animated fadeIn" style="animation-delay: 0ms;">智慧短信</li> </a><a href="/sms" class="mr-3 color-default"><li class="about-item fnbold mt-3 animated fadeIn" style="animation-delay: 0ms;">短信</li> </a><a href="/shorturl" class="mr-3 color-default"><li class="about-item fnbold mt-3 animated fadeIn" style="animation-delay: 0ms;">短网址</li> </a></ul>
                    </div>
              `
                },
            ]
        },
        {
            title: '政企行业',
            children: [
                {
                    icon: '/static/libraries_v4/view/home/<USER>/homeicon/gov1.png',
                    q: 'G2G 管理',
                    a: `
                    <div class="sub-nav-content" style="width:100%">
                        <h5 class="sub-nav-title">G2G 管理</h5>
                        <p class="sub-nav-text mt-3">提升政府各机构之间的沟通效率、协作能力和响应速度，推进电子政务建设，加强政府数字化转型和上下级协调和管理，实现 G2G管理自动化、透明化、高效化。</p>
                        <div class="case mt-3">
                            <div class="case-left">
                                <div class="case-logo mr-3 ml-2">
                                    <img src="/static/libraries_v4/view/home/<USER>/section_4/logo/wh.png" alt="">
                                </div>
                                <div class="case-info">
                                    <a href="/gover"><div class="case-name d-flex align-items-center">武汉市文化和旅游局 <span class="ml-1 icon iconfont4 icon-jiantou_1 roda-arrow"></span></div> </a>
                                    <div class="case-text mt-1">武汉市人民政府职能工作部门</div>
                                </div>

                            </div>
                            <div class="case-rate ">
                                <div class="rate-item mr-3">
                                    <div class="rate-num d-flex align-items-center">
                                        <span class="number" data-target="30">30</span>
                                            <div class="ml-1 text-center num-info">
                                                <img class="up-down roat-deg" src="/static/libraries_v4/view/home/<USER>/down.svg">
                                                <div class="fn18 color-default">%</div>
                                            </div>
                                        </div>
                                    <div class="rate-name fn12">部门协作效率</div>
                                </div>
                                <div class="rate-item mr-2">
                                    <div class="rate-num d-flex align-items-center">
                                        <span class="number" data-target="38">38</span>
                                            <div class="ml-1 text-center num-info">
                                                <img class="up-down roat-deg" src="/static/libraries_v4/view/home/<USER>/down.svg">
                                                <div class="fn18 color-default">%</div>
                                            </div>
                                        </div>
                                    <div class="rate-name fn12">数智化程度</div>
                                </div>
                            </div>
                        </div>
                        <p class="about-title mt-4 mb-0">相关产品</p>
                        <ul class="about-list"></ul>
                    </div>
              `
                },
                {
                    icon: '/static/libraries_v4/view/home/<USER>/homeicon/gov2.png',
                    q: 'G2B 管理',
                    a: `
                    <div class="sub-nav-content" style="width:100%">
                        <h5 class="sub-nav-title">G2B 管理</h5>
                        <p class="sub-nav-text mt-3">通过云通信平台，加强与企业的双向互动，构建新型政商关系。优化营商环境的同时，也更方便地对企业进行监管，促进经济发展和社会进步。</p>
                        <div class="case mt-3">
                            <div class="case-left">
                                <div class="case-logo mr-3 ml-2">
                                    <img src="/static/libraries_v4/view/home/<USER>/section_4/logo/wbank.png" alt="">
                                </div>
                                <div class="case-info">
                                    <a href="/gover"><div class="case-name d-flex align-items-center">威海商业银行 <span class="ml-1 icon iconfont4 icon-jiantou_1 roda-arrow"></span></div> </a>
                                    <div class="case-text mt-1">威海市股份制“全国十佳城市商业银行”</div>
                                </div>

                            </div>
                            <div class="case-rate ">
                                <div class="rate-item mr-3">
                                    <div class="rate-num d-flex align-items-center">
                                        <span class="number" data-target="20">20</span>
                                            <div class="ml-1 text-center num-info">
                                                <img class="up-down roat-deg" src="/static/libraries_v4/view/home/<USER>/down.svg">
                                                <div class="fn18 color-default">%</div>
                                            </div>
                                        </div>
                                    <div class="rate-name fn12">政企互信度</div>
                                </div>
                                <div class="rate-item mr-2">
                                    <div class="rate-num d-flex align-items-center">
                                        <span class="number" data-target="28">28</span>
                                            <div class="ml-1 text-center num-info">
                                                <img class="up-down roat-deg" src="/static/libraries_v4/view/home/<USER>/down.svg">
                                                <div class="fn18 color-default">%</div>
                                            </div>
                                        </div>
                                    <div class="rate-name fn12">政务处理速度</div>
                                </div>
                            </div>
                        </div>
                        <p class="about-title mt-4 mb-0">相关产品</p>
                        <ul class="about-list"></ul>
                    </div>`
                },
                {
                    icon: '/static/libraries_v4/view/home/<USER>/homeicon/gov3.png',
                    q: 'G2C 管理',
                    a: `
                    <div class="sub-nav-content" style="width:100%">
                        <h5 class="sub-nav-title">G2C 管理</h5>
                        <p class="sub-nav-text mt-3">政府可以向个人发送各类信息，以便民众及时了解配合政府相关工作开展；民众也可通过云通信服务向政府反馈问题和建议，帮助政府更好地解决社会问题。</p>
                        <div class="case mt-3">
                            <div class="case-left">
                                <div class="case-logo mr-3 ml-2">
                                    <img src="/static/libraries_v4/view/home/<USER>/section_4/logo/gz.png" alt="">
                                </div>
                                <div class="case-info">
                                    <a href="/gover"><div class="case-name d-flex align-items-center">广州地铁 <span class="ml-1 icon iconfont4 icon-jiantou_1 roda-arrow"></span></div> </a>
                                    <div class="case-text mt-1">一体化、便捷化、人性化的轨道交通服务</div>
                                </div>

                            </div>
                            <div class="case-rate ">
                                <div class="rate-item mr-3">
                                    <div class="rate-num d-flex align-items-center">
                                        <span class="number" data-target="22">22</span>
                                            <div class="ml-1 text-center num-info">
                                                <img class="up-down roat-deg" src="/static/libraries_v4/view/home/<USER>/down.svg">
                                                <div class="fn18 color-default">%</div>
                                            </div>
                                        </div>
                                    <div class="rate-name fn12">信息传播度</div>
                                </div>
                                <div class="rate-item mr-2">
                                    <div class="rate-num d-flex align-items-center">
                                        <span class="number" data-target="36">36</span>
                                            <div class="ml-1 text-center num-info">
                                                <img class="up-down roat-deg" src="/static/libraries_v4/view/home/<USER>/down.svg">
                                                <div class="fn18 color-default">s</div>
                                            </div>
                                        </div>
                                    <div class="rate-name fn12">民众参与度</div>
                                </div>
                            </div>
                        </div>
                        <p class="about-title mt-4 mb-0">相关产品</p>
                        <ul class="about-list"></ul>
                    </div>
              `
                },
            ]
        }
        , {
            title: '游戏行业',
            children: [
                {
                    icon: '/static/libraries_v4/view/home/<USER>/homeicon/game1.png',
                    q: '游戏研发',
                    a: `
                    <div class="sub-nav-content" style="width:100%">
                        <h5 class="sub-nav-title">游戏研发</h5>
                        <p class="sub-nav-text mt-3">云通信服务在游戏研发时，可用于邀请玩家参与测试调研；在游戏运营中，可用于处理玩家的问题、投诉或建议。可以提高玩家的参与度和留存率，助力市场表现。</p>
                        <div class="case mt-3">
                            <div class="case-left">
                                <div class="case-logo mr-3 ml-2">
                                    <img src="/static/libraries_v4/view/home/<USER>/section_4/logo/wy.png" alt="">
                                </div>
                                <div class="case-info">
                                    <a href="/solugame"><div class="case-name d-flex align-items-center">网易游戏 <span class="ml-1 icon iconfont4 icon-jiantou_1 roda-arrow"></span></div> </a>
                                    <div class="case-text mt-1">网游自主研发领域的前端的中国游戏公司</div>
                                </div>

                            </div>
                            <div class="case-rate ">
                                <div class="rate-item mr-3">
                                    <div class="rate-num d-flex align-items-center">
                                        <span class="number" data-target="24">24</span>
                                            <div class="ml-1 text-center num-info">
                                                <img class="up-down roat-deg" src="/static/libraries_v4/view/home/<USER>/down.svg">
                                                <div class="fn18 color-default">%</div>
                                            </div>
                                        </div>
                                    <div class="rate-name fn12">用户留存率</div>
                                </div>
                                <div class="rate-item mr-2">
                                    <div class="rate-num d-flex align-items-center">
                                        <span class="number" data-target="5">5</span>
                                            <div class="ml-1 text-center num-info">
                                                <img class="up-down roat-deg" src="/static/libraries_v4/view/home/<USER>/down.svg">
                                                <div class="fn18 color-default">s</div>
                                            </div>
                                        </div>
                                    <div class="rate-name fn12">响应速度</div>
                                </div>
                            </div>
                        </div>
                        <p class="about-title mt-4 mb-0">相关产品</p>
                        <ul class="about-list"></ul>
                    </div>
              `
                },
                {
                    icon: '/static/libraries_v4/view/home/<USER>/homeicon/game2.png',
                    q: '社区论坛',
                    a: `
                    <div class="sub-nav-content" style="width:100%">
                        <h5 class="sub-nav-title">社区论坛</h5>
                        <p class="sub-nav-text mt-3">向用户发送最新通知，确保用户能够及时获取相关信息；鼓励用户之间进行互动，增强社区的社交性和互动性，吸引更多玩家关注，促进社区论坛的运营。</p>
                        <div class="case mt-3">
                            <div class="case-left">
                                <div class="case-logo mr-3 ml-2">
                                    <img src="/static/libraries_v4/view/home/<USER>/partner/logo/partner_33.png" alt="">
                                </div>
                                <div class="case-info">
                                    <a href="/solugame"><div class="case-name d-flex align-items-center">小黑盒 <span class="ml-1 icon iconfont4 icon-jiantou_1 roda-arrow"></span></div> </a>
                                    <div class="case-text mt-1">为Steam游戏玩家定制的高能玩家聚集地</div>
                                </div>

                            </div>
                            <div class="case-rate ">
                                <div class="rate-item mr-3">
                                    <div class="rate-num d-flex align-items-center">
                                        <span class="number" data-target="30">30</span>
                                            <div class="ml-1 text-center num-info">
                                                <img class="up-down roat-deg" src="/static/libraries_v4/view/home/<USER>/down.svg">
                                                <div class="fn18 color-default">%</div>
                                            </div>
                                        </div>
                                    <div class="rate-name fn12">通知速度</div>
                                </div>
                                <div class="rate-item mr-2">
                                    <div class="rate-num d-flex align-items-center">
                                        <span class="number" data-target="28">28</span>
                                            <div class="ml-1 text-center num-info">
                                                <img class="up-down " src="/static/libraries_v4/view/home/<USER>/down.svg">
                                                <div class="fn18 color-default">%</div>
                                            </div>
                                        </div>
                                    <div class="rate-name fn12">运营成本</div>
                                </div>
                            </div>
                        </div>
                        <p class="about-title mt-4 mb-0">相关产品</p>
                        <ul class="about-list"></ul>
                    </div>`
                },
                {
                    icon: '/static/libraries_v4/view/home/<USER>/homeicon/game3.png',
                    q: '交易平台',
                    a: `
                    <div class="sub-nav-content" style="width:100%">
                        <h5 class="sub-nav-title">交易平台</h5>
                        <p class="sub-nav-text mt-3">利用好云通信服务可以增加平台收入，为玩家提供更优质的服务和体验。同时也有助于提高平台的知名度和竞争力，吸引更多的玩家和用户。</p>
                        <div class="case mt-3">
                            <div class="case-left">
                                <div class="case-logo mr-3 ml-2">
                                    <img src="/static/libraries_v4/view/home/<USER>/section_4/logo/tx.png" alt="">
                                </div>
                                <div class="case-info">
                                    <a href="/solugame"><div class="case-name d-flex align-items-center">腾讯游戏 <span class="ml-1 icon iconfont4 icon-jiantou_1 roda-arrow"></span></div> </a>
                                    <div class="case-text mt-1">致力于为用户创造高品质数字游戏生活体验</div>
                                </div>

                            </div>
                            <div class="case-rate ">
                                <div class="rate-item mr-3">
                                    <div class="rate-num d-flex align-items-center">
                                        <span class="number" data-target="19">19</span>
                                            <div class="ml-1 text-center num-info">
                                                <img class="up-down roat-deg" src="/static/libraries_v4/view/home/<USER>/down.svg">
                                                <div class="fn18 color-default">%</div>
                                            </div>
                                        </div>
                                    <div class="rate-name fn12">平台竞争力</div>
                                </div>
                                <div class="rate-item mr-2">
                                    <div class="rate-num d-flex align-items-center">
                                        <span class="number" data-target="40">40</span>
                                            <div class="ml-1 text-center num-info">
                                                <img class="up-down roat-deg" src="/static/libraries_v4/view/home/<USER>/down.svg">
                                                <div class="fn18 color-default">%</div>
                                            </div>
                                        </div>
                                    <div class="rate-name fn12">玩家参与度</div>
                                </div>
                            </div>
                        </div>
                        <p class="about-title mt-4 mb-0">相关产品</p>
                        <ul class="about-list"></ul>
                    </div>
              `
                },
            ]
        }
    ];

    banner.init();
    free.init();
    nav.init();
    s7_swiper.init();
    wufen.init();
    shuju.init();
    new Faq2(faqlist);


});



$(document).ready(function () {
    $('.section-2 input.free-input').focus(function () {
        $('.section-2 input.test_code,.code-show > img').addClass('active');
        $('.free-exper').attr('style', 'margin-right:1rem!important');
        $('.free-exper input').attr('style', 'margin-right: 12px;');

    });
    // 滚动动画
    const swiperFn = function (swiperObj) {
        const INIT_TRANSFORM_X = -$(swiperObj)[0].clientWidth / 2;  // transform3d初始值  滚动列表宽度的二分之一
        let speed = 10; // 移动速度
        let timer; // 自动轮播定时器

        let transformValue = INIT_TRANSFORM_X; // transform3d值
        let clickPos = 0;  // 鼠标初次点击时的位置（基准点）
        let moveValue = 0;  // 偏移量
        let isMouseDown = false;  // 是否拖拽状态

        const startSwiper = function () {
            if (timer) clearInterval(timer);
            timer = setInterval(function () {
                transformValue += 1;
                if (transformValue >= 0) {
                    transformValue = INIT_TRANSFORM_X;
                }
                $(swiperObj)[0].style.transform = `translate3d(${transformValue}px, 0px, 0px)`;
            }, speed);
        };

        const continuteSwiper = function () {
            if (!isMouseDown) return;
            isMouseDown = false;
            transformValue += moveValue;
            moveValue = 0;
            $(swiperObj).removeClass("ent-list-grabbing");
            startSwiper();
        };
        $(swiperObj).on('mouseenter', function (e) {
            speed = 30;
            // console.log('enter');
            startSwiper();
        });

        $(swiperObj).on('mouseleave', function (e) {
            speed = 10;
            startSwiper();
            // continuteSwiper();
        });

        $(swiperObj).on('mousemove', function (e) {
            if (!isMouseDown) return;
            moveValue = e.clientX - clickPos;

            // 往右
            if (transformValue + moveValue >= 0) {
                clickPos = e.clientX;
                transformValue = INIT_TRANSFORM_X;
                return;
                // 往左
            } else if (transformValue + moveValue <= INIT_TRANSFORM_X) {
                clickPos = e.clientX;
                transformValue = 0;
            }
            $(swiperObj)[0].style.transform = `translate3d(${transformValue + moveValue}px, 0px, 0px)`;
        });

        $(swiperObj).on('mousedown', function (e) {
            isMouseDown = true;
            clickPos = e.clientX;
            $(swiperObj).addClass("team-swiper-list-grabbing");
            clearInterval(timer);
        });

        $('body').on('mouseup', function (e) {
            continuteSwiper();
        });

        startSwiper();
    };
    swiperFn($('.section6-list'));


    $('.section-7 .souce-left .left-card .card-btn1').hover(function () {
        $('.scal-img1').addClass('active');
    }, function () {
        $('.scal-img1').removeClass('active');
    });

    $('.section-7 .souce-left .left-card .card-btn2').hover(function () {
        $('.scal-img2').addClass('active');
    }, function () {
        $('.scal-img2').removeClass('active');
    });


    let _html = `
    <span class="ml-1 icon iconfont4 icon-jiantou_1 roda-arrow"></span>
    `;
    $('.register-section .my-button').attr('style', 'display: flex;margin: auto;align-items: center;justify-content: center;');
    $('.register-section .my-button').append(_html);
});




