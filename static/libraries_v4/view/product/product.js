/**
 * 产品页常见组建的js
 * <AUTHOR>
*/

/**
 * 常见问题组件的构造函数
 * 使用时 html中先引入该文件
 * 直接实例化构造函数new Faq(list)
 * @param list 参数
*/
function Faq(list) {
  this.activeIndex = 0;
  this.list = list;

  this.init = function () {
    this.initDots();
    this.setDot(0);
    $('#faq-colls').on('hide.bs.collapse', function (e) {
      $(e.target).prev().removeClass('show');
    });
    $('#faq-colls').on('show.bs.collapse', function (e) {
      $(e.target).prev().addClass('show');
    });
  };

  this.initDots = function () {
    var _this = this;
    var _lis = '';
    this.list.forEach(function (item, index) {
      _lis += `<div class="dot-item fn14" data-index="${index}">${item.title}</div>`;
    });
    $('.product-faq .dots').empty().append(_lis);
    $('.product-faq .dot-item').on('click', function () {
      var index = $(this).data('index');
      if (index == this.activeIndex) return;
      _this.activeIndex = index;
      _this.setDot(index);
    });
  };

  this.setDot = function (index) {
    $('.product-faq .dots').children().each(function () {
      $(this).removeClass("active");
    }).eq(index).addClass("active");

    var _collItems = '';
    this.list[index].children.forEach(function (item, index) {
      _collItems += `  
        <div class="coll-item">  
          <div class="coll-header ${index == 0 ? 'show' : ''}" id="head-${index}">  
              <div class="header-content" href="#cont-${index}" aria-controls="cont-${index}" data-toggle="collapse" role="button" aria-expanded="${index == 0 ? true : false}">  
                  <span>${item.q}</span>  
                  <img class="jiantou" src="/static/libraries_v4/view/product/sms/imgs/jiantou.png" alt="">  
              </div>  
          </div>  
          <div id="cont-${index}" class="collapse ${index == 0 ? 'show' : ''}" aria-labelledby="head-${index}">  
              <div class="body-content">${item.a}</div>  
          </div>  
        </div>  
      `;
      $('#faq-colls').empty().append(_collItems);
    });
  };
  this.init();
}
function Faq2(list) {
  this.activeIndex = 0;
  this.list = list;
  this.init = function () {
    this.initDots();
    this.setDot(0);
    $('#faq-colls').on('hide.bs.collapse', function (e) {
      $(e.target).prev().removeClass('show');
    });
    $('#faq-colls').on('show.bs.collapse', function (e) {
      $(e.target).prev().addClass('show');
    });
  };

  this.initDots = function () {
    var _this = this;
    var _lis = '';
    this.list.forEach(function (item, index) {
      _lis += `<div class="dot-item fn14" data-index="${index}">${item.title}</div>`;
    });
    $('.product-faq .dots').empty().append(_lis);
    $('.product-faq .dot-item').on('click', function () {
      var index = $(this).data('index');
      if (index == this.activeIndex) return;
      _this.activeIndex = index;
      _this.setDot(index);
    });
  };

  this.setDot = function (index) {
    $('.product-faq .dots').children().each(function () {
      $(this).removeClass("active");
    }).eq(index).addClass("active");

    var _collItems = '';
    this.list[index].children.forEach(function (item, index) {
      _collItems += `  
        <div class="coll-item">  
          <div class="coll-header" id="head-${index}">  
              <div class="header-content" href="#cont-${index}" aria-controls="cont-${index}" data-toggle="collapse" role="button" aria-expanded="${index == 0 ? true : false}">  
                  <span class="d-flex align-items-center"> <img class="icon-han" src="${item.icon}" alt=""> ${item.q}</span>  
                  <img class="jiantou" src="/static/libraries_v4/view/product/sms/imgs/jiantou.png" alt="">  
              </div>  
          </div>  
          <div id="cont-${index}" class="collapse" aria-labelledby="head-${index}">  
              <div class="body-content">${item.a}</div>  
          </div>  
        </div>  
      `;
      $('#faq-colls').empty().append(_collItems);
    });

    function getRandomArrayElements(arr, count) {
      var shuffled = arr.slice(0), i = arr.length, min = i - count, temp, index;
      while (i-- > min) {
        index = Math.floor((i + 1) * Math.random());
        temp = shuffled[index];
        shuffled[index] = shuffled[i];
        shuffled[i] = temp;
      }
      return shuffled.slice(min);
    }

    let items = [
      {
        title: '短信',
        href: '/sms'
      },
      {
        title: '邮件',
        href: '/mail'
      },
      {
        title: '多媒体彩信',
        href: '/mms'
      },
      {
        title: '语音',
        href: '/voice'
      },
      {
        title: '短网址',
        href: '/shorturl'
      },
      {
        title: '国际短信',
        href: '/internationalsms'
      },
      {
        title: '智慧短信',
        href: '/smartsms'
      },
      {
        title: '免密登录',
        href: '/onepass'
      },
      {
        title: '身份验证',
        href: '/factor'
      },
      {
        title: 'AIM 短信',
        href: '/rcs'
      },
    ];
    var da = "";
    $.each(getRandomArrayElements(items, 3), function (i, d) {
      da += `<a href="${d.href}" class="mr-3 color-default"><li class="about-item fnbold mt-3 animated fadeIn" style="animation-delay: 0ms;">${d.title}</li> </a>`;
    });
    $('#product-faq ul.about-list').html(da);



  };
  this.init();
}
function Footer(list) {
  this.activeIndex = 0;
  this.list = list;


  this.init = function () {
    this.initDots();
    this.setDot(0);
    $('#faq-colls').on('hide.bs.collapse', function (e) {
      $(e.target).prev().removeClass('show');
    });
    $('#faq-colls').on('show.bs.collapse', function (e) {
      $(e.target).prev().addClass('show');
    });
  };

  this.initDots = function () {
    var _this = this;
    var _lis = '';
    this.list.forEach(function (item, index) {
      // _lis += `<div class="dot-item fn14" data-index="${index}">${item.title}</div>`;
    });
    $('.product-faq2 .dots').empty().append(_lis);
    $('.product-faq2 .dot-item').on('click', function () {
      var index = $(this).data('index');
      if (index == this.activeIndex) return;
      _this.activeIndex = index;
      _this.setDot(index);
    });
  };

  this.setDot = function (index) {
    $('.product-faq2 .dots').children().each(function () {
      $(this).removeClass("active");
    }).eq(index).addClass("active");

    var _collItems = '';
    this.list[index].children.forEach(function (item, index) {
      _collItems += `  
      <div class="coll-item">  
        <div class="coll-header " id="head-${index}">  
            <div class="header-content" href="#cont-${index}" aria-controls="cont-${index}" data-toggle="collapse" role="button" aria-expanded="${index == 0 ? true : false}">  
                <span>${item.q}</span>  
                <img class="jiantou" src="/static/libraries_v4/view/product/sms/imgs/jiantou.png" alt="">  
            </div>  
        </div>  
        <div id="cont-${index}" class="collapse" aria-labelledby="head-${index}">  
            <div class="body-content">${item.a}</div>  
        </div>  
      </div>  
    `;
      $('#faq-colls2').empty().append(_collItems);
    });
  };
  this.init();
}



/**
 * 产品介绍右侧带手机的组件
 * @param {string[]} imgs 两张图片链接
*/


// function setImgAnimate1() {
//   $('.mode-img1 .news-img1').addClass('active');
//   setTimeout(function () {
//     $('.mode-img1 .news-img2').addClass('active');
//   }, 300);

// }


// function setImgAnimate2() {
//   setTimeout(function () {
//     $('.mode-img2 .news-img1').addClass('active');
//   }, 100);

//   setTimeout(function () {
//     $('.mode-img2 .news-img2').addClass('active');
//   }, 300);

// }


function ProductServeMobile(imgs) {
  this.index = -1;
  this.imgs = imgs;
  this.init = function () {
    this.setIndex(0);
    var that = this;
    $('.product-info .card-content').on('click', function () {
      that.setIndex($(this).parent().index());
    });
  };

  this.setIndex = function (index) {
    if (this.index == index) return;
    this.index = index;
    $('.product-info .card-content').removeClass('active');
    $('.product-info .card-item').eq(index).find('.card-content').addClass('active');

    $('.phone-mode .phone-mode-img').eq(index).show().siblings().hide();
    $('.phone-mode .phone-mode-img').eq(index).addClass('active').siblings().removeClass('active');


    this.setMobile(index);
  };

  this.setMobile = function (index) {
    var _img = `
              <img src="${this.imgs[index]}" class="animated myzoomIn" alt="" />
          `;
    $(".product-info .content-img").empty().append(_img);
  };
  this.init();
}


function ProductServeMobile2(imgs) {
  this.index = -1;
  this.imgs = imgs;
  this.init = function () {
    this.setIndex(0);
    var that = this;
    $('.serve-dots .serve-dot-item').on('click', function () {
      that.setIndex($(this).index());
    });
  };

  this.setIndex = function (index) {
    if (this.index == index) return;
    this.index = index;
    $('.serve-dots .serve-dot-item').removeClass('active');
    $('.serve-dots .serve-dot-item').eq(index).addClass('active');

    $('.phone-mode .phone-mode-img').eq(index).show().siblings().hide();
    $('.phone-mode .phone-mode-img').eq(index).addClass('active').siblings().removeClass('active');

    this.setMobile(index);
  };

  this.setMobile = function (index) {
    var _img = `
              <img src="${this.imgs[index]}" class="animated myzoomIn" alt="" />
          `;
    $("#product-info .content-img").empty().append(_img);
  };
  this.init();
}