function Faq(t){this.activeIndex=0,this.list=t,this.init=function(){this.initDots(),this.setDot(0),$("#faq-colls").on("hide.bs.collapse",(function(t){$(t.target).prev().removeClass("show")})),$("#faq-colls").on("show.bs.collapse",(function(t){$(t.target).prev().addClass("show")}))},this.initDots=function(){var t=this,i="";this.list.forEach((function(t,e){i+=`<div class="dot-item fn14" data-index="${e}">${t.title}</div>`})),$(".product-faq .dots").empty().append(i),$(".product-faq .dot-item").on("click",(function(){var i=$(this).data("index");i!=this.activeIndex&&(t.activeIndex=i,t.setDot(i))}))},this.setDot=function(t){$(".product-faq .dots").children().each((function(){$(this).removeClass("active")})).eq(t).addClass("active");var i="";this.list[t].children.forEach((function(t,e){i+=`  \n        <div class="coll-item">  \n          <div class="coll-header ${0==e?"show":""}" id="head-${e}">  \n              <div class="header-content" href="#cont-${e}" aria-controls="cont-${e}" data-toggle="collapse" role="button" aria-expanded="${0==e}">  \n                  <span>${t.q}</span>  \n                  <img class="jiantou" src="/libraries_v4/view/product/sms/imgs/jiantou.png" alt="">  \n              </div>  \n          </div>  \n          <div id="cont-${e}" class="collapse ${0==e?"show":""}" aria-labelledby="head-${e}">  \n              <div class="body-content">${t.a}</div>  \n          </div>  \n        </div>  \n      `,$("#faq-colls").empty().append(i)}))},this.init()}function Faq2(t){this.activeIndex=0,this.list=t,this.init=function(){this.initDots(),this.setDot(0),$("#faq-colls").on("hide.bs.collapse",(function(t){$(t.target).prev().removeClass("show")})),$("#faq-colls").on("show.bs.collapse",(function(t){$(t.target).prev().addClass("show")}))},this.initDots=function(){var t=this,i="";this.list.forEach((function(t,e){i+=`<div class="dot-item fn14" data-index="${e}">${t.title}</div>`})),$(".product-faq .dots").empty().append(i),$(".product-faq .dot-item").on("click",(function(){var i=$(this).data("index");i!=this.activeIndex&&(t.activeIndex=i,t.setDot(i))}))},this.setDot=function(t){$(".product-faq .dots").children().each((function(){$(this).removeClass("active")})).eq(t).addClass("active");var i="";this.list[t].children.forEach((function(t,e){i+=`  \n        <div class="coll-item">  \n          <div class="coll-header" id="head-${e}">  \n              <div class="header-content" href="#cont-${e}" aria-controls="cont-${e}" data-toggle="collapse" role="button" aria-expanded="${0==e}">  \n                  <span class="d-flex align-items-center"> <img class="icon-han" src="${t.icon}" alt=""> ${t.q}</span>  \n                  <img class="jiantou" src="/libraries_v4/view/product/sms/imgs/jiantou.png" alt="">  \n              </div>  \n          </div>  \n          <div id="cont-${e}" class="collapse" aria-labelledby="head-${e}">  \n              <div class="body-content">${t.a}</div>  \n          </div>  \n        </div>  \n      `,$("#faq-colls").empty().append(i)}));var e="";$.each(function(t,i){for(var e,s,n=t.slice(0),a=t.length,o=a-i;a-- >o;)e=n[s=Math.floor((a+1)*Math.random())],n[s]=n[a],n[a]=e;return n.slice(o)}([{title:"短信",href:"/sms"},{title:"邮件",href:"/mail"},{title:"多媒体彩信",href:"/mms"},{title:"语音",href:"/voice"},{title:"短网址",href:"/shorturl"},{title:"国际短信",href:"/internationalsms"},{title:"智慧短信",href:"/smartsms"},{title:"免密登录",href:"/onepass"},{title:"身份验证",href:"/factor"},{title:"AIM 短信",href:"/rcs"}],3),(function(t,i){e+=`<a href="${i.href}" class="mr-3 color-default"><li class="about-item fnbold mt-3 animated fadeIn" style="animation-delay: 0ms;">${i.title}</li> </a>`})),$("#product-faq ul.about-list").html(e)},this.init()}function Footer(t){this.activeIndex=0,this.list=t,this.init=function(){this.initDots(),this.setDot(0),$("#faq-colls").on("hide.bs.collapse",(function(t){$(t.target).prev().removeClass("show")})),$("#faq-colls").on("show.bs.collapse",(function(t){$(t.target).prev().addClass("show")}))},this.initDots=function(){var t=this;this.list.forEach((function(t,i){})),$(".product-faq2 .dots").empty().append(""),$(".product-faq2 .dot-item").on("click",(function(){var i=$(this).data("index");i!=this.activeIndex&&(t.activeIndex=i,t.setDot(i))}))},this.setDot=function(t){$(".product-faq2 .dots").children().each((function(){$(this).removeClass("active")})).eq(t).addClass("active");var i="";this.list[t].children.forEach((function(t,e){i+=`  \n      <div class="coll-item">  \n        <div class="coll-header " id="head-${e}">  \n            <div class="header-content" href="#cont-${e}" aria-controls="cont-${e}" data-toggle="collapse" role="button" aria-expanded="${0==e}">  \n                <span>${t.q}</span>  \n                <img class="jiantou" src="/libraries_v4/view/product/sms/imgs/jiantou.png" alt="">  \n            </div>  \n        </div>  \n        <div id="cont-${e}" class="collapse" aria-labelledby="head-${e}">  \n            <div class="body-content">${t.a}</div>  \n        </div>  \n      </div>  \n    `,$("#faq-colls2").empty().append(i)}))},this.init()}function ProductServeMobile(t){this.index=-1,this.imgs=t,this.init=function(){this.setIndex(0);var t=this;$(".product-info .card-content").on("click",(function(){t.setIndex($(this).parent().index())}))},this.setIndex=function(t){this.index!=t&&(this.index=t,$(".product-info .card-content").removeClass("active"),$(".product-info .card-item").eq(t).find(".card-content").addClass("active"),$(".phone-mode .phone-mode-img").eq(t).show().siblings().hide(),$(".phone-mode .phone-mode-img").eq(t).addClass("active").siblings().removeClass("active"),this.setMobile(t))},this.setMobile=function(t){var i=`\n              <img src="${this.imgs[t]}" class="animated myzoomIn" alt="" />\n          `;$(".product-info .content-img").empty().append(i)},this.init()}function ProductServeMobile2(t){this.index=-1,this.imgs=t,this.init=function(){this.setIndex(0);var t=this;$(".serve-dots .serve-dot-item").on("click",(function(){t.setIndex($(this).index())}))},this.setIndex=function(t){this.index!=t&&(this.index=t,$(".serve-dots .serve-dot-item").removeClass("active"),$(".serve-dots .serve-dot-item").eq(t).addClass("active"),$(".phone-mode .phone-mode-img").eq(t).show().siblings().hide(),$(".phone-mode .phone-mode-img").eq(t).addClass("active").siblings().removeClass("active"),this.setMobile(t))},this.setMobile=function(t){var i=`\n              <img src="${this.imgs[t]}" class="animated myzoomIn" alt="" />\n          `;$("#product-info .content-img").empty().append(i)},this.init()}