/* 产品页-模版样式 */
.product-container {
  width: 100%;
  overflow-x: hidden;
}
.fontFamily {
  font-family: "BarlowSemiCondensedSemiBold";
}
.bto-line {
  padding-bottom: 12px;
  border-bottom: 1px solid #e1e6ed;
}

@media (max-width: 768px) {
  .pro-tex-desc {
    padding-right: 1rem !important;
  }
  .btom-text {
    display: none;
  }

  .product-info .card-item:nth-child(1) .card-content.active::before,
  .product-info .card-item:nth-child(2) .card-content.active::before,
  .product-three-cell .card-item::before {
    background-image: none !important;
  }
}
/* 通用模版-banner-start */
.product-banner {
  position: relative;
  background-color: #f1f4fd;
  height: 540px;
}
.product-banner .container {
  height: 100%;
}
.product-banner .banner-bg-img {
  position: absolute;
  top: 0;
  left: 0;
  /* pointer-events: none; */
  user-select: none;
  object-fit: cover;
  width: 100%;
  height: 100%;
}
.product-banner .banner-content {
  position: absolute;
  width: 100%;
  height: 100%;
  max-width: 480px;
  display: flex;
  flex-direction: column;
  justify-content: center;
}
.product-banner .fav {
  color: #fff;
  height: 34px;
  line-height: 34px;
  display: flex;
  align-items: center;
  position: relative;
  background: linear-gradient(to right, #28bfe0 80%, transparent);
  width: 94%;
}
.product-banner .fav span {
  position: relative;
  z-index: 1;
}
.product-banner .fav .img_icon {
  width: 20px;
  height: 20px;
  object-fit: contain;
}
/* .product-banner .fav::after {
  content: "";
  position: absolute;
  z-index: 0;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;

  background-image: -moz-linear-gradient(
    0deg,
    rgb(40, 190, 223) 0%,
    rgb(40, 191, 224) 100%
  );
  background-image: -webkit-linear-gradient(
    0deg,
    rgb(40, 190, 223) 0%,
    rgb(40, 191, 224) 100%
  );
  background-image: -ms-linear-gradient(
    0deg,
    rgb(40, 190, 223) 0%,
    rgb(40, 191, 224) 100%
  );
  transform: skew(-7deg, 0);
} */
.product-banner .my-button {
  min-width: 110px;
}

@media screen and (max-width: 575px) {
  .product-banner .banner-content {
    max-width: calc(100% - 30px);
  }
  .product-banner .fav span {
    line-height: 1.2;
    font-size: 13px;
  }
  .product-banner .my-button {
    min-width: 100px;
  }
}

/* 通用模版-banner-end */
/* 通用模版-锚点-start */

.product-anchors {
  background-color: #fff;
  height: 60px;
  width: 100%;
  position: absolute;
  left: 0;
  bottom: 0;
  z-index: 1;
}
.product-anchors .container {
  height: 100%;
}
.product-anchors .anchors-list {
  width: 100%;
  display: flex;
  align-items: center;
  height: 100%;
}
.product-anchors .anchors-item {
  flex: 1;
  color: rgba(26, 28, 31) !important;
  font-size: 14px;
}
.product-anchors .anchors-item:hover {
  color: #1764ff !important;
  /* text-decoration: underline !important; */
}

/* 通用模版-锚点-end */
/* 通用模版-产品介绍-start如/sms */

.product-info {
  padding: 72px 0 80px 0;
  scroll-margin-top: 60px;
  background-color: #f5f7fb;
}

.product-info .info-card {
  height: 420px;
  padding: 32px 24px 32px 32px;
  border: 2px solid #fff;
  background-image: -moz-linear-gradient(
    90deg,
    rgb(255, 255, 255) 0%,
    rgb(243, 245, 249) 100%
  );
  background-image: -webkit-linear-gradient(
    90deg,
    rgb(255, 255, 255) 0%,
    rgb(243, 245, 249) 100%
  );
  background-image: -ms-linear-gradient(
    90deg,
    rgb(255, 255, 255) 0%,
    rgb(243, 245, 249) 100%
  );
  box-shadow: 8px 8px 20px 0 rgba(55, 99, 170, 0.1);
  border-radius: 8px;
  width: 80%;
}
.product-info .title {
  color: #41464f;
}
.product-info .card-content {
  min-height: 122px;
  padding-top: 24px;
  padding-bottom: 24px;
  background-image: -moz-linear-gradient(
    90deg,
    rgb(255, 255, 255) 0%,
    rgb(243, 245, 249) 100%
  );
  background-image: -webkit-linear-gradient(
    90deg,
    rgb(255, 255, 255) 0%,
    rgb(243, 245, 249) 100%
  );
  background-image: -ms-linear-gradient(
    90deg,
    rgb(255, 255, 255) 0%,
    rgb(243, 245, 249) 100%
  );
  cursor: pointer;
  position: relative;
  z-index: 1;
}
.product-info .card-content::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  background-repeat: no-repeat;
  background-size: 54% auto;
  background-position: 225% -117%;
  transition: all 0.2s ease-out;
  z-index: -1;
}
.product-info .card-content.active::before {
  background-position: 125% -40%;
}
.product-info .card-content.active {
  box-shadow: 0 4px 10px rgba(55, 99, 170, 0.1);
}
.product-info .card-content::after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  border: 1px solid #e1e6ed;
  border-radius: 4px;
  transition: all 0.3s;
  pointer-events: none;
}
.product-info .card-content.active::after {
  border: 2px solid #fff;
}
@media screen and (max-width: 1199px) {
  .product-info .card-content.active {
    background-size: 100% auto;
  }
}
.product-info .card-content .tag-item {
  padding: 0 8px;
  font-size: 12px;
  background-color: rgba(231, 234, 239, 0.8);
}
.product-info .card-content h6 {
  transition: all 0.3s;
}
.product-info .card-content.active h6 {
  color: #1764ff;
}
.product-info .info-supers {
  font-weight: bold;
}
.product-info .info-supers img {
  width: 32px;
  height: 32px;
  object-fit: contain;
}
.product-info .mobile-case {
  position: absolute;
  top: -58px;
  right: 400px;
  z-index: 1;
}
.product-info .mobile-case .phone-mode {
  position: absolute;
  top: 0;
  left: 0;
  width: 390px;
  height: 627px;
  z-index: 2;
  background: url("../home/<USER>/section_4/phone_bg.png")
    no-repeat 0 0 / contain;
}
.product-info .mobile-case .phone-content {
  position: relative;
}
.product-info .mobile-case .content-img {
  position: absolute;
  top: 17px;
  left: 8px;
  width: 242px;
  height: 514px;
  z-index: 1;
  border-radius: 20px;
  overflow: hidden;
}
.product-info .mobile-case .content-img img {
  width: 100%;
  height: 100%;
  object-fit: fill;
  animation-name: myzoomIn;
  animation-duration: 0.4s;
  animation-timing-function: cubic-bezier(0.25, 0.1, 0.25, 1);
  animation-delay: 0s;
  animation-iteration-count: 1;
  animation-direction: normal;
  animation-fill-mode: both;
  animation-play-state: running;
}
.product-info .mobile-case .content-bg {
  background-color: #fff;
  position: absolute;
  top: 17px;
  left: 8px;
  width: 242px;
  height: 514px;
  z-index: 0;
  border-radius: 20px;
}

@keyframes myzoomIn {
  0% {
    transform: scale(1.05);
    opacity: 0;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}
@media screen and (max-width: 1199px) {
  .product-info .mobile-case {
    display: none;
  }
  .product-info .info-card {
    width: 100%;
    height: fit-content;
  }
  .product-info .card-cards {
    padding-right: 0 !important;
  }
}
@media screen and (max-width: 575px) {
  .product-info .tags {
    padding-right: 0 !important;
  }
}

/* 通用模版-产品介绍-end */
/* 通用模版-产品介绍样式2-start如/mail */

.product-info-web {
  padding: 60px 0 80px;
  scroll-margin-top: 60px;
}
.product-info-web .info-card-web {
  width: 100%;
  border-radius: 4px;
  box-shadow: 0px 10px 40px 0px rgba(0, 17, 86, 0.1);
  border: 2px solid #fff;
  background-image: -moz-linear-gradient(
    90deg,
    rgb(255, 255, 255) 0%,
    rgb(243, 245, 249) 100%
  );
  background-image: -webkit-linear-gradient(
    90deg,
    rgb(255, 255, 255) 0%,
    rgb(243, 245, 249) 100%
  );
  background-image: -ms-linear-gradient(
    90deg,
    rgb(255, 255, 255) 0%,
    rgb(243, 245, 249) 100%
  );
}
.product-info-web .serve-dots {
  display: flex;
  flex-wrap: wrap;
}
.product-info-web .serve-dot-item {
  height: 56px;
  display: flex;
  align-items: center;
  padding: 0 24px;
  border: 1px solid rgb(206, 212, 221);
  background-image: -moz-linear-gradient(
    90deg,
    rgb(255, 255, 255) 0%,
    rgb(243, 245, 249) 100%
  );
  background-image: -webkit-linear-gradient(
    90deg,
    rgb(255, 255, 255) 0%,
    rgb(243, 245, 249) 100%
  );
  background-image: -ms-linear-gradient(
    90deg,
    rgb(255, 255, 255) 0%,
    rgb(243, 245, 249) 100%
  );
  border-radius: 2px;
  text-align: center;
  position: relative;
  transition: all 0.3s;
  cursor: pointer;
}
.product-info-web .serve-dot-item::after {
  content: "";
  width: 2px;
  height: 35%;
  background-color: rgb(206, 212, 221);
  position: absolute;
  top: 50%;
  left: 0;
  transform: translateY(-50%);
}
.product-info-web .serve-dot-item.active {
  color: #1764ff;
  border-color: #fff;
  box-shadow: 0 4px 10px rgba(55, 99, 170, 0.1);
}
.product-info-web .serve-dot-item.active::after {
  background-color: #1764ff;
}
.product-info-web .serve-list {
  display: flex;
}
.product-info-web .serve-item {
  width: 33%;
  max-width: 168px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  padding-left: 8px;
  border-left: 1px solid #ced4dd;
  line-height: 20px;
  padding-left: 20px;
}
.product-info-web .serve-item.no-border {
  border: none;
  padding-left: 10px;
}
.product-info-web .info-card-web .img-wrap {
  height: 100%;
}
.product-info-web .info-card-web .img-wrap img {
  width: 440px;
  height: auto;
  /* height: 100%;
  object-fit: cover; */
}

@media screen and (max-width: 1199px) {
  .product-info-web .right-img {
    display: none;
  }
}

@media screen and (max-width: 768px) {
  #canvas-container {
    display: none;
  }
  .product-info {
    padding: 40px 0 30px 0;
  }
  .banner-content {
    text-align: center;
  }
  .banner-content .btn-top {
    display: flex;
    justify-content: center;
  }
  .banner-img-title {
    transform: translateY(-40px);
  }
  .banner-fu-title {
    width: 90%;
    margin: 4px auto !important;
    transform: translateY(-48px);
  }
  .product-banner {
    height: 450px;
  }
  .product-info .title,
  .fu-title {
    font-size: 14px;
  }
  .product-banner .fav {
    display: none;
  }
  .product-info-web .serve-wrap {
    padding-left: 16px !important;
    padding-right: 16px !important;
  }
  .product-info-web .serve-dot-item {
    padding: 0 8px;
    height: 36px;
    line-height: 36px;
    font-size: 14px;
  }
  .product-info-web .serve-item {
    padding-left: 8px !important;
  }
  .product-info-web .serve-item.no-border {
    padding-left: 0 !important;
  }
  .product-info-web .serve-item-title {
    font-size: 14px;
  }
  .product-info-web .serve-item .fn18 {
    font-size: 16px;
  }
  .product-info-web .serve-item .fn16 {
    font-size: 14px;
  }
  .product-info-web .serve-item .fn14 {
    font-size: 12px;
  }
}

/* 通用模版-产品介绍样式2-end */
/* 通用模版-功能优势-start */

.product-six-cell {
  padding: 72px 0 30px;
  background: #fff;
  position: relative;
  scroll-margin-top: 60px;
}
.product-six-cell .title {
  color: #41464f;
}
.product-six-cell .card-list {
  display: flex;
  justify-content: space-between;
  margin: 0 -0.5rem;
  flex-wrap: wrap;
  margin-top: 7rem;
}
.product-six-cell .card-list .card-item {
  position: relative;
  padding: 36px 30px;
  border-radius: 6px;
  width: 100%;
  background-color: #f3f5f9;
  height: 100%;
}
.product-six-cell .card-list .card-item .title {
  font-weight: bold;
  font-size: 18px;
  color: #1a1c1f;
}
.product-six-cell .card-list .card-item .text {
  font-size: 14px;
  color: #41464f;
}
.product-six-cell .card-list .card-item img {
  position: absolute;
  top: 0;
  right: 20px;
  transform: translateY(-50%);
  width: 120px;
  /* height: 60px; */
  object-fit: contain;
  pointer-events: none;
  user-select: none;
}
.product-six-cell .card-list li {
  margin-bottom: 4rem !important;
}
@media (max-width: 768px) {
  .product-six-cell .card-list .card-item .title {
    font-size: 18px !important;
  }
  .product-six-cell .card-list {
    margin-top: 5rem !important;
  }
  .product-six-cell {
    padding: 40px 0 0px;
  }
}

/* 通用模版-功能优势-end */
/* 通用模版-三宫格(左中右)-start */

.product-three-cell {
  background-color: #f5f7fb;
  padding: 72px 0;
  scroll-margin-top: 60px;
  margin-top: 2rem;
}
.product-three-cell .card-item {
  border: 2px solid #fff;
  border-radius: 4px;
  height: 100%;
  background-image: -moz-linear-gradient(
    90deg,
    rgb(255, 255, 255) 0%,
    rgb(243, 245, 249) 100%
  );
  background-image: -webkit-linear-gradient(
    90deg,
    rgb(255, 255, 255) 0%,
    rgb(243, 245, 249) 100%
  );
  background-image: -ms-linear-gradient(
    90deg,
    rgb(255, 255, 255) 0%,
    rgb(243, 245, 249) 100%
  );
  box-shadow: 0px 5px 15px 0px rgba(0, 17, 86, 0.1);
  z-index: 1;
}
.product-three-cell .card-content {
  height: 50%;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}
.product-three-cell .send-icon {
  width: 36px;
  height: 36px;
  object-fit: contain;
}
.product-three-cell .three-icon {
  width: 36px;
  height: 36px;
  object-fit: contain;
}
.product-three-cell .low-price {
  color: rgb(26, 28, 31);
}
.product-three-cell .low-price b {
  font-weight: 500;
}
.product-three-cell .more-btn {
  border-color: #1764ff;
  color: #1764ff !important;
}
.product-three-cell .go-buy {
  border-color: #1764ff;
  color: #fff !important;
}
.product-three-cell .more-btn:hover {
  text-decoration: none !important;
  background-color: #1764ff !important;
  color: #fff !important;
}
.product-three-cell .tag-item {
  font-size: 14px;
  padding: 0 10px;
}
.product-three-cell .gt {
  transition: all 0.3s ease;
  position: relative;
  top: 0;
  left: 0;
  transform: rotate(0) scale(0.7);
  display: inline-block;
  font-weight: 300;
  font-size: 12px;
}
.product-three-cell a.link:hover {
  color: #1764ff;
  /* text-decoration: underline !important; */
}
.product-three-cell a.link:hover .gt {
  transform: rotate(45deg) scale(0.75);
}
@media (max-width: 768px) {
  .product-three-cell {
    margin-top: 0;
  }
  .product-three-cell .card-content .title {
    font-size: 18px;
  }
  .product-three-cell {
    padding: 40px 0;
  }
}
/* 通用模版-三宫格(左中右)-end */
/* 通用模版-常见问题FAQ-start */

.product-faq {
  padding: 72px 0 40px;
  scroll-margin-top: 60px;
}
.product-faq .dots {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
}
.product-faq .dot-item {
  background-color: #f3f5f9;
  border-radius: 2px;
  padding: 0 16px;
  height: 28px;
  line-height: 28px;
  margin: 8px 8px 0 0;
  text-align: center;
  cursor: pointer;
  transition: background-color 0.3s ease-out;
  user-select: none;
  /* font-size: 12px !important; */
}
.product-faq .dot-item.active {
  background-color: #1764ff;
  color: #fff;
}

.product-faq-index .dot-item.active {
  color: #1764ff;
  background-color: #fff;
}
.product-faq-index .dot-item {
  background: none;
}

.product-faq .coll-item {
  margin-top: 1rem;
}
.product-faq .header-content {
  border: 1px solid transparent;
  border-radius: 4px;
  transition: border-color 0.3s ease;
  cursor: pointer;
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-weight: 500;
  padding: 16px 1.5rem;
}
.product-faq .header-content:hover {
  border-color: #e1e6ed;
}
.product-faq .coll-header.show .header-content {
  border-color: #e1e6ed;
  border-radius: 4px 4px 0 0;
  border-bottom-color: transparent;
}
.product-faq img.jiantou {
  width: 10px;
  height: 10px;
  object-fit: contain;
  transform: rotate(-180deg);
  transition: transform 0.3s ease;
}
.product-faq .coll-header.show img.jiantou {
  transform: rotate(0);
}
.header-content.show img.jiantou {
  transform: rotate(0);
}

.product-faq .body-content {
  background-color: rgb(245, 247, 251);
  box-shadow: inset 0px 0px 10px 1px rgba(0, 15, 15, 0.1);
  padding: 24px 1.5rem;
  border-radius: 0 0 4px 4px;
  font-size: 14px;
  color: #41464f;
}
.product-faq .more-q a:hover {
  color: #1764ff;
}
@media (max-width: 768px) {
  .product-faq {
    padding: 40px 0 40px;
  }
}
@media screen and (max-width: 575px) {
  .product-faq .dot-item {
    width: calc(25% - 6px);
    padding: 0;
  }
  .product-faq .dot-item:nth-child(4n + 4) {
    margin-right: 0;
  }
  .product-faq .header-content {
    font-size: 14px;
  }
}

/* 通用模版-常见问题FAQ-end */
/* 通用模版-折叠面板-start */

/* 通用模版-折叠面板-end */
/* 通用模版-六宫格-start */
/* 通用模版-六宫格-end */

/* @media screen and (max-width: 991px) {
}
@media screen and (max-width: 767px) {
}
@media screen and (min-width: 992px) and (max-width: 991px) {
}
@media screen and (min-width: 768px) and (max-width: 991px) {
}
@media screen and (min-width: 576px) and (max-width: 767px) {
}
@media screen and (max-width: 575px) {
} */

.product-three-cell .card-item {
  position: relative;
  overflow: hidden;
  padding-right: 0 !important;
}

.product-three-cell .card-item::before {
  content: "";
  z-index: -1;
  position: absolute;
  bottom: 0;
  right: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  background-repeat: no-repeat;
  background-size: 84% auto;
  background-position: 13rem 11rem;
  background-image: url("sms/imgs/moneyPack.png");
}

/* 产品动画 */
.right-img .img-wrap {
  background: #d1deef;
}
.right-img .img-wrap img {
  position: absolute;
  left: 12px;
  top: 10px;
}
.img-txt {
  opacity: 0;
  width: 0;
  position: absolute;
  /* top: 4rem;
  left: 11rem; */
  z-index: 1;
  color: #fff;
  background: rgba(0, 0, 0, 0.8);
  font-size: 14px;
  height: 34px;
  line-height: 34px;
  padding-left: 14px;
  border-radius: 26px;
  overflow: hidden;
}

.txt-title1 span,
.txt-title2 span,
.txt-title3 span,
.txt-title4 span,
.txt-title5 span {
  opacity: 0;
  display: inline-block;
  transform: translateX(-10px);
  transition: all 0.3s;
}
.txt-title2 span,
.txt-title3 span,
.txt-title4 span,
.txt-title5 span {
  color: #282b31;
  font-weight: bold;
  font-size: 12px;
  left: 55px;
}

@media (max-width: 768px) {
  .product-first {
    margin-top: 2rem !important;
  }
  .product-six-cell .title {
    font-size: 14px;
  }
  .register-section h2 {
    font-size: 24px !important;
  }
  .register-section p {
    font-size: 14px;
  }
}
