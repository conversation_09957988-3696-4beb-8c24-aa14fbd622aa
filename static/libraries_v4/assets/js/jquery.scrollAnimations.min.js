(function($,window,document,undefined){var pluginName="scrollAnimations",defaults={offset:.8};var timer;function ScrollAnimations(element,options){if(element){this.element=element;this.animationElements=[];this.triggerPoint=null;this.lastScrollPos=-1;this.options=$.extend({},defaults,options);this._defaults=defaults;this._name=pluginName;window.onload=this.init()}}ScrollAnimations.prototype={init:function(){var _this=this;var $els=$(this.element);_this.setup($els);var scrollIntervalID=setInterval(function(){_this.updatePage(_this)},10);$(window).on("resize",function(){_this.resize()})},resize:function(){var _this=this;clearTimeout(timer);timer=setTimeout(function(){_this.setTriggerpoint()},50)},setTriggerpoint:function(){this.triggerPoint=window.innerHeight*this.options.offset},setup:function(items){this.setTriggerpoint();var $this=$(items),$children=$this.find("[data-animation-child]");if($children.length){$children.each(function(){var $child=$(this);var $delay=$child.attr("data-animation-delay");$child.css({"-webkit-animation-delay":$delay,"-moz-animation-delay":$delay,"-ms-animation-delay":$delay,"-o-animation-delay":$delay,"animation-delay":$delay})})}else{var $delay=$this.attr("data-animation-delay");$this.css({"-webkit-animation-delay":$delay,"-moz-animation-delay":$delay,"-ms-animation-delay":$delay,"-o-animation-delay":$delay,"animation-delay":$delay})}this.animationElements.push($this)},updatePage:function(plugin){var _this=plugin;window.requestAnimationFrame(function(){_this.animateElements()})},animateElements:function(){var _this=this;var scrollPos=window.pageYOffset;if(scrollPos===this.lastScrollPos)return;this.lastScrollPos=scrollPos;$(_this.animationElements).each(function(){var $this=$(this),$children=$this.find("[data-animation-child]");if($this.hasClass("animated")||scrollPos<$this.offset().top-_this.triggerPoint)return;if($children.length){$this.addClass("animated");$children.each(function(){$(this).addClass("animated").addClass($(this).attr("data-animation"))})}else{$this.addClass("animated").addClass($this.attr("data-animation"))}})}};$.fn[pluginName]=function(options){return this.each(function(){if(!$.data(this,"plugin_"+pluginName)){$.data(this,"plugin_"+pluginName,new ScrollAnimations(this,options))}})};if(typeof define==="function"&&define.amd){define(function(){return ScrollAnimations})}})(jQuery,window,document);