/* 自定义样式文件 - 从模板中提取的内联样式 */

/* 十周年相关样式 */
.ten-years {
    color: #DE2626;
    background-image: url("../libraries_v4/view/tenYears/imgs/tenYears/ten_ico.jpg");
    background-repeat: no-repeat;
    background-size: cover;
}

.ten-years:hover {
    border: 1px solid #ffbe6b;
}

.ten-years:hover h6 {
    color: #FD7D81 !important;
}

/* 云生态相关样式 */
.about-sumail {
    background-image: url("../libraries_v4/view/cloudEcology/imgs/coulds1.png");
    background-repeat: no-repeat;
    background-size: cover;
}

.top-submail {
    background-image: url("../libraries_v4/view/cloudEcology/imgs/coulds3.png");
    background-repeat: no-repeat;
    background-size: cover;
}

.sea-submail {
    background-image: url("../libraries_v4/view/cloudEcology/imgs/coulds4.png");
    background-repeat: no-repeat;
    background-size: cover;
}

/* 布局相关样式 */
.content {
    width: 100%;
    padding-right: 0 !important;
    padding-left: 0 !important;
    margin-right: auto;
    margin-left: auto;
}

/* 按钮样式 */
.new-ask {
    background: linear-gradient(to bottom, #4d88ff, #1764ff);
    text-align: center;
    width: 54px;
    padding: 16px 0;
    border-radius: 34px;
    margin: auto;
    position: relative;
    right: -0.9rem;
    top: -1rem;
    box-shadow: 0px 0px 15px rgb(0 0 0 / 14%);
    border: 2px solid #fff;
}

/* 九周年相关样式 */
.nine-year {
    display: inline-block;
    width: 56px;
    position: relative;
    margin-bottom: 10px;
    right: -3rem;
}

#promotion img {
    width: 100%;
}

/* 侧边栏相关样式 */
#badge_num {
    position: absolute;
    left: 3rem;
    top: -2.6rem;
    z-index: 1000;
}

/* 联系方式样式 */
.wx-text {
    width: 240px;
    text-align: left;
    left: -15.3rem;
    padding: 10px 20px;
}

.wx-text div {
    font-size: 15px;
}

.wx-text hr {
    background: #c1c1c1;
    margin: 10px 10px 6px 0;
}

.wx-text span {
    font-size: 13px;
    color: #c1c1c1;
}

/* 页脚图标样式 */
.footer-icon {
    width: 36px;
}

.footer-text {
    color: #1a1c1f;
}

/* 底部分隔线样式 */
.footer-line {
    border-bottom-color: #e1e6ed;
}

/* 页脚布局样式 */
.footer-layout {
    justify-content: space-between;
}

/* 二维码容器样式 */
.qr-container {
    width: 110px;
}

/* Logo容器样式 */
.logo-container {
    width: 160px;
}

/* 移动端导航按钮样式 */
.nav-btn {
    width: 60px;
}

/* 推广相关样式 */
.promotion-img {
    border-radius: 30px;
    box-shadow: 0px 0px 15px rgb(0 0 0 / 14%);
}

.promotion-container {
    width: 120px;
    position: relative;
    left: 1rem;
    top: -12px;
}

/* 头部导航相关样式 */
.contact-business-link {
    margin-top: 2rem;
}

.seek-phone-spacing {
    margin-top: 6rem;
}

.ten-years-title {
    color: #DE2626;
}

.ten-years-subtitle {
    color: rgba(222, 38, 38, 0.6);
}

/* 移动端导航样式 */
.sidebar-border {
    border-right: 1px solid #e1e6ed;
}

.nav-border-bottom {
    border-bottom: 1px solid #b2bbd0;
}
