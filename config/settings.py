"""
Django settings for config project.

由'django-admin startproject'使用Django 5.2.1生成。

如需了解更多关于此文件的信息，请参阅
https://docs.djangoproject.com/en/5.2/topics/settings/

有关设置及其值的完整列表，请参阅
https://docs.djangoproject.com/en/5.2/ref/settings/
"""

import os
from pathlib import Path
from dotenv import load_dotenv

# 加载环境变量
env_file = '.env.develop' if os.environ.get('DJANGO_ENV') != 'production' else '.env.product'
load_dotenv(env_file)

# 建立项目路径
BASE_DIR = Path(__file__).resolve().parent.parent


# 快速启动开发设置 - 不适合生产环境
# 参见 https://docs.djangoproject.com/en/5.2/howto/deployment/checklist/

# SECURITY WARNING: keep the secret key used in production secret!
SECRET_KEY = 'django-insecure-j6a6@r+btq+yr)$oac_oa@^#)cm&p*ht9mv@-tnv@#cn=oeh^m'

# SECURITY WARNING: don't run with debug turned on in production!
DEBUG = os.environ.get('DEBUG', 'False').lower() == 'true'

ALLOWED_HOSTS = ['localhost', '127.0.0.1']


# Application definition

INSTALLED_APPS = [
    'django.contrib.admin',
    'django.contrib.auth',
    'django.contrib.contenttypes',
    'django.contrib.sessions',
    'django.contrib.messages',
    'django.contrib.staticfiles',

    # Tailwind CSS
    'tailwind',
    'theme',
    'apps.news'
]

# 在非生产环境下启用django_browser_reload
if os.environ.get('DJANGO_ENV') != 'production':
    INSTALLED_APPS.append('django_browser_reload')

# 中间件配置
MIDDLEWARE = [
    'django.middleware.security.SecurityMiddleware',
    'whitenoise.middleware.WhiteNoiseMiddleware',
    'django.contrib.sessions.middleware.SessionMiddleware',
    'django.middleware.common.CommonMiddleware',
    'django.middleware.csrf.CsrfViewMiddleware',
    'django.contrib.auth.middleware.AuthenticationMiddleware',
    'django.contrib.messages.middleware.MessageMiddleware',
    'django.middleware.clickjacking.XFrameOptionsMiddleware',
]

# 在非生产环境下启用django_browser_reload中间件
if os.environ.get('DJANGO_ENV') != 'production':
    MIDDLEWARE.append('django_browser_reload.middleware.BrowserReloadMiddleware')

ROOT_URLCONF = 'config.urls'

TEMPLATES = [
    {
        'BACKEND': 'django.template.backends.django.DjangoTemplates',
        'DIRS': [BASE_DIR / 'templates'],
        'APP_DIRS': True,
        'OPTIONS': {
            'context_processors': [
                'django.template.context_processors.request',
                'django.contrib.auth.context_processors.auth',
                'django.contrib.messages.context_processors.messages',
            ],
        },
    },
]

WSGI_APPLICATION = 'config.wsgi.application'


# 数据库配置
# https://docs.djangoproject.com/en/5.2/ref/settings/#databases

DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.sqlite3',  # 使用SQLite数据库引擎
        'NAME': BASE_DIR / 'db.sqlite3',  # 数据库名称和路径
    }
}


# 密码验证
# https://docs.djangoproject.com/en/5.2/ref/settings/#auth-password-validators

AUTH_PASSWORD_VALIDATORS = [
    {
        'NAME': 'django.contrib.auth.password_validation.UserAttributeSimilarityValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.MinimumLengthValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.CommonPasswordValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.NumericPasswordValidator',
    },
]


# 国际化设置
LANGUAGE_CODE = 'zh-hans'  # 设置语言代码为简体中文

TIME_ZONE = 'Asia/Shanghai'  # 设置时区为中国上海

USE_I18N = True  # 启用国际化

USE_TZ = False  # 启用时区支持


# 静态文件（CSS、JavaScript、图片）
# https://docs.djangoproject.com/en/5.2/howto/static-files/

STATIC_URL = 'static/'  # 设置静态文件URL前缀
STATIC_ROOT = BASE_DIR / 'staticfiles'  # 设置静态文件根目录
STATICFILES_DIRS = [
    BASE_DIR / 'static',
]

# 媒体文件配置
MEDIA_URL = 'media/'  # 设置媒体文件URL前缀
MEDIA_ROOT = BASE_DIR / 'media'  # 设置媒体文件根目录

# 默认主键字段类型
# https://docs.djangoproject.com/en/5.2/ref/settings/#default-auto-field

DEFAULT_AUTO_FIELD = 'django.db.models.BigAutoField'

# Tailwind CSS 配置
TAILWIND_APP_NAME = 'theme'

# 设置 npm 可执行文件路径
NPM_BIN_PATH = 'npm.cmd'

# 开发模式下启用 Tailwind CSS 自动编译
INTERNAL_IPS = [
    "127.0.0.1",
]

# 生产模式下启用 WhiteNoise 静态文件服务
STORAGES = {
    "staticfiles": {
        "BACKEND": "whitenoise.storage.CompressedStaticFilesStorage",
    },
    "default": {
        "BACKEND": "django.core.files.storage.FileSystemStorage",
    },
}

# 配置WhiteNoise
WHITENOISE_MANIFEST_STRICT = False