# Sierting.com

思而听（山东）网络科技有限公司官方网站，使用 Django 5 和 Tailwind CSS 4 构建。

## 公司简介

思而听（山东）网络科技有限公司是山东山科控股集团-正中信息参股设立的网络安全公司，省属三级国有企业。拥有完整自主知识产权的网络安全产品、安全管理平台、安全服务与解决方案，是国内最早涉足青少年网络安全培训的企业之一。

“思而听”以满足用户需求为根本动力，经过多年耕耘，已成为政府、高校、电信、金融、税务、能源、交通等方面的重要客户进行网络安全服务和网络安全培训的首选品牌。

## 项目概述

这个项目是思而听（山东）网络科技有限公司的官方网站，基于 Django 5.2.1 和 Tailwind CSS 4.1.0 构建。网站提供了响应式设计和现代化的用户界面，展示公司产品、服务和企业文化。项目使用了 Django 的模板系统和 Tailwind CSS 的原子化 CSS 框架，实现了快速开发和灵活的样式定制。

## 技术栈

- **后端框架**: Django 5.2.1
- **前端框架**: Tailwind CSS 4.1.0
- **包管理工具**: uv (Python 现代化包管理工具)，npm (Node.js)
- **开发工具**: Django Browser Reload (热重载)
- **数据库**: SQLite (开发环境)
- **语言**: Python 3.12+, JavaScript

## 网站内容结构

官方网站包含以下主要板块：

- **公司介绍**：展示思而听（山东）网络科技有限公司的企业概况、文化和使命
- **网络安全产品**：展示公司的网络安全产品和解决方案
- **安全服务**：介绍公司提供的各类网络安全服务
- **青少年安全培训**：展示青少年网络安全培训课程和活动
- **成功案例**：展示在政府、高校、电信等领域的成功案例
- **新闻动态**：发布公司最新的新闻和行业动态
- **联系方式**：提供公司的联系方式和位置信息

## 技术特点

- **响应式设计**：自动适应不同屏幕尺寸，提供最佳用户体验
- **现代化界面**：使用 Tailwind CSS 构建美观、专业的用户界面
- **高效开发**：使用 Django 和 Tailwind CSS 实现快速开发和迭代
- **国际化支持**：默认使用简体中文，支持多语言切换

## 安装指南

### 前提条件

- Python 3.12 或更高版本
- Node.js 和 npm
- uv 包管理工具 (安装方法: `pip install uv`)

### 安装步骤

1. 克隆仓库

```bash
git clone https://github.com/sierting/sierting.com.git
cd sierting.com
```

2. 创建并激活虚拟环境

```bash
# 使用 uv 创建虚拟环境
uv venv

# Windows
.venv\Scripts\activate
# Linux/macOS
source .venv/bin/activate
```

3. 安装 Python 依赖

```bash
# 使用 uv add 安装依赖，比 pip install 更快速、更安全
uv add cookiecutter django django-browser-reload django-tailwind

# 或者从 pyproject.toml 安装所有依赖
uv pip sync
```

4. 安装 Tailwind CSS 依赖

```bash
cd theme/static_src
npm install
```

5. 编译 Tailwind CSS

```bash
npm run build
```

6. 运行数据库迁移

```bash
cd ../..
# 使用 uv run 运行 Django 命令
uv run python manage.py migrate
```

7. 启动开发服务器

```bash
# 使用 uv run 启动开发服务器
uv run python manage.py runserver
```

现在，您可以在浏览器中访问 http://127.0.0.1:8000/ 查看网站。

## 开发指南

### 开发工作流程

在开发过程中，您需要同时运行 Django 服务器和 Tailwind CSS 编译器。以下是推荐的开发工作流程：

1. **启动 Tailwind CSS 编译器**

   打开一个终端窗口，运行：
   ```bash
   # 在项目根目录下
   uv run python manage.py tailwind start
   # 或在 Windows 下
   uv run .\manage.py tailwind start
   ```

2. **启动 Django 开发服务器**

   打开另一个终端窗口，运行：
   ```bash
   # 在项目根目录下
   uv run python manage.py runserver
   ```

3. **开发工作流程**

   - 修改 HTML 模板、CSS 或 Python 文件
   - Tailwind CSS 编译器会自动检测变化并重新编译样式
   - Django Browser Reload 会自动刷新浏览器页面
   - 在浏览器中实时查看更改效果

这种设置允许您在修改代码后立即看到效果，大大提高开发效率。

### 项目结构

```
sierting.com/
├── config/                 # 项目配置目录
│   ├── settings.py         # Django 设置
│   ├── urls.py             # URL 路由配置
│   ├── views.py            # 视图函数
│   ├── wsgi.py             # WSGI 配置
│   └── asgi.py             # ASGI 配置
├── templates/              # 全局模板目录
│   ├── base.html           # 基础模板
│   └── index.html          # 首页模板
├── theme/                  # Tailwind CSS 主题应用
│   ├── static_src/         # Tailwind CSS 源文件
│   │   ├── src/            # CSS 源文件
│   │   └── package.json    # npm 配置
│   └── static/             # 编译后的静态文件
├── manage.py               # Django 管理脚本
├── pyproject.toml          # Python 项目配置
└── README.md               # 项目文档
```

### Tailwind CSS 开发方式

除了上面的开发工作流程中介绍的使用 Django Tailwind 命令启动 Tailwind CSS 编译器外，您还可以直接使用 npm 来启动 Tailwind CSS 的实时编译：

```bash
cd theme/static_src
npm run dev
```

这种方式也会启动 Tailwind CSS 的监视模式，但可能需要手动配置文件扫描路径。

#### 构建生产版本的 Tailwind CSS

当您完成开发并准备部署时，可以使用以下命令构建优化的生产版本 CSS：

```bash
# 使用 Django Tailwind 命令
# 在项目根目录下运行
uv run python manage.py tailwind build

# 或者直接使用 npm
cd theme/static_src
npm run build
```

这将生成一个经过压缩和优化的 CSS 文件，适合用于生产环境。

### Django 开发

Django 项目使用标准的 Django 开发流程：

1. 创建新的应用

```bash
# 使用 uv run 创建新应用
uv run python manage.py startapp myapp
```

2. 添加新的视图、模型和模板
3. 更新 URL 配置
4. 运行迁移

```bash
# 使用 uv run 执行 Django 命令
uv run python manage.py makemigrations
uv run python manage.py migrate
```

## 部署指南

### 生产环境设置

在部署到生产环境之前，请确保：

1. 更新 `config/settings.py` 中的安全设置：
   - 设置 `DEBUG = False`
   - 更新 `SECRET_KEY`
   - 配置 `ALLOWED_HOSTS`

2. 编译生产版本的 Tailwind CSS（参见上面的“构建生产版本的 Tailwind CSS”部分）

3. 收集静态文件：

```bash
# 使用 uv run 收集静态文件
uv run python manage.py collectstatic
```

4. 配置数据库（如 PostgreSQL）
5. 设置 WSGI/ASGI 服务器（如 Gunicorn、Uvicorn）
6. 配置反向代理（如 Nginx）

## 使用 uv 的优势

使用 uv 作为 Python 包管理工具有以下优势：

- **速度快**：uv 是用 Rust 编写的，比传统的 pip 快 10-100 倍
- **依赖解析**：更智能的依赖解析，减少冲突
- **可重现的构建**：通过 uv.lock 文件确保依赖版本一致
- **虚拟环境管理**：内置虚拟环境创建和管理功能
- **兼容性**：与现有的 Python 工具链完全兼容

### 常用 uv 命令

```bash
# 创建虚拟环境
uv venv

# 安装单个包
uv add package_name

# 从 pyproject.toml 安装依赖
uv pip sync

# 运行 Python 脚本
uv run python script.py

# 运行 Django 命令
uv run python manage.py command

# 更新所有依赖
uv pip sync --upgrade
```

## 贡献指南

欢迎贡献代码、报告问题或提出改进建议。请遵循以下步骤：

1. Fork 仓库
2. 创建功能分支 (`git checkout -b feature/amazing-feature`)
3. 提交更改 (`git commit -m 'Add some amazing feature'`)
4. 推送到分支 (`git push origin feature/amazing-feature`)
5. 创建 Pull Request

## 版权声明

本网站及其内容版权归思而听（山东）网络科技有限公司所有。未经允许，不得复制、传播、使用或用于商业目的。

## 联系方式

如有任何问题或建议，请通过以下方式联系我们：

- **公司名称**：思而听（山东）网络科技有限公司
- **地址**：山东省济南市市中区凯瑞大厦
- **服务热线**：400-6136-816
- **业务部电话**：18894665383
- **邮箱**：<EMAIL>
- **邮政区码**：250000
- **网址**：www.sierting.com
- **GitHub Issues**：[https://github.com/sierting/sierting.com/issues](https://github.com/sierting/sierting.com/issues)
