#!/usr/bin/env python
"""Django的命令行实用工具，用于执行管理任务。

生成于'django-admin startproject'使用Django 5.2.1。

如需了解更多关于此文件的信息，请参阅
https://docs.djangoproject.com/en/5.2/topics/settings/
"""
import os
import sys


def main():
    """运行管理任务的主函数。"""
    # 设置默认的DJANGO_SETTINGS_MODULE环境变量
    os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings')
    try:
        # 导入Django核心管理模块
        from django.core.management import execute_from_command_line
    except ImportError as exc:
        # 如果导入失败，抛出详细的错误信息
        raise ImportError(
            "无法导入Django。请确认是否已安装Django，并且"
            "PYTHONPATH环境变量配置正确？是否忘记激活虚拟环境？"
        ) from exc
    # 执行命令行参数指定的任务
    execute_from_command_line(sys.argv)


if __name__ == '__main__':
    # 当作为主程序运行时，调用main函数
    main()