services:
  web:
    build: .
    command: gunicorn config.wsgi:application --bind 0.0.0.0:8000
    volumes:
      - .:/app
    expose:
      - 8000
    env_file:
      - .env.product

  nginx:
    image: nginx:1.25
    ports:
      - "80:80"
    volumes:
      - ./nginx/default.conf:/etc/nginx/conf.d/default.conf
      - ./static:/app/static
      - ./staticfiles:/app/staticfiles
      - ./media:/app/media
    depends_on:
      - web