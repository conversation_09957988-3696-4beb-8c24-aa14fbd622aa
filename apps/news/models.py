from django.db import models

from config.models import BaseModel


# 文章分类
class Category(BaseModel):
    """文章分类"""
    name = models.CharField(max_length=50, verbose_name='分类名称')
    parent = models.ForeignKey('self', on_delete=models.CASCADE, related_name='children', verbose_name='父级分类',
                               null=True, blank=True)

    class Meta:
        verbose_name = '文章分类'
        verbose_name_plural = verbose_name

    def __str__(self):
        return self.name


class Article(BaseModel):
    """文章"""
    STATUS_CHOICES = (
        ('p', '发布'),
        ('d', '草稿'),
    )

    category = models.ForeignKey(Category, on_delete=models.CASCADE, verbose_name='分类')
    title = models.CharField(max_length=100, verbose_name='标题')
    keywords = models.CharField(max_length=200, verbose_name='关键词')
    description = models.Char<PERSON>ield(max_length=300, verbose_name='描述')
    content = models.TextField(verbose_name='内容')
    image = models.ImageField(upload_to='news/%Y/%m/%d/', verbose_name='图片')
    status = models.CharField(max_length=1, choices=STATUS_CHOICES, default='p', verbose_name='状态')
    views = models.PositiveIntegerField(default=0, verbose_name='浏览量')

    class Meta:
        verbose_name = '文章'
        verbose_name_plural = verbose_name
        ordering = ['-created_at']

    def __str__(self):
        return self.title

    def increase_views(self):
        """增加浏览量"""
        self.views += 1
        self.save(update_fields=['views'])
