from django.contrib import admin
from .models import Category, Article


@admin.register(Category)
class CategoryAdmin(admin.ModelAdmin):
    list_display = ['name', 'parent']
    search_fields = ['name']
    list_filter = ['parent']
    list_display_links = ['name']
    list_per_page = 10
    ordering = ['id']
    fieldsets = (
        ('基本信息', {'fields': ('name', 'parent',)}),
    )  # 详细信息的显示


@admin.register(Article)
class ArticleAdmin(admin.ModelAdmin):
    list_display = ['title', 'category', 'status', 'views', 'created_at']
    search_fields = ['title', 'content']
    list_filter = ['category', 'status', 'created_at']
    list_display_links = ['title']
    list_per_page = 10
    ordering = ['-created_at']
    fieldsets = (
        ('基本信息', {'fields': ('title', 'category', 'status',)}),
        ('内容信息', {'fields': ('keywords', 'description', 'content', 'image',)}),
    )  # 详细信息的显示
    list_editable = ['status', ]  # 列表页可编辑
