from django.shortcuts import render, get_object_or_404
from django.views.generic import ListView, DetailView
from django.db.models import Q

from .models import Article, Category


class ArticleListView(ListView):
    """文章列表视图"""
    model = Article
    template_name = 'news/list.html'
    context_object_name = 'articles'
    paginate_by = 10

    def get_queryset(self):
        queryset = super().get_queryset().filter(status='p')
        # 搜索功能
        query = self.request.GET.get('q')
        if query:
            queryset = queryset.filter(
                Q(title__icontains=query) |
                Q(description__icontains=query) |
                Q(content__icontains=query)
            )
        return queryset

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        # 获取所有分类
        context['categories'] = Category.objects.all()
        # 设置活动分类
        context['active_category'] = None
        context['active_category_name'] = '全部文章'
        return context


class ArticleDetailView(DetailView):
    """文章详情视图"""
    model = Article
    template_name = 'news/detail.html'
    context_object_name = 'article'

    def get_queryset(self):
        return super().get_queryset().filter(status='p')

    def get_object(self, queryset=None):
        # 获取对象并增加浏览量
        obj = super().get_object(queryset=queryset)
        obj.increase_views()
        return obj

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        # 获取相关文章
        article = self.object
        related_articles = Article.objects.filter(
            category=article.category,
            status='p'
        ).exclude(id=article.id)[:5]
        context['related_articles'] = related_articles
        return context


class CategoryArticleListView(ListView):
    """分类文章列表视图"""
    model = Article
    template_name = 'news/list.html'  # 使用相同的模板
    context_object_name = 'articles'
    paginate_by = 10

    def get_queryset(self):
        # 获取分类
        category_id = self.kwargs.get('category_id')
        category = get_object_or_404(Category, id=category_id)
        # 获取该分类及其子分类的所有文章
        category_ids = [category.id]
        for child in category.children.all():
            category_ids.append(child.id)
        return Article.objects.filter(category_id__in=category_ids, status='p')

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        # 获取所有分类
        context['categories'] = Category.objects.all()
        # 设置活动分类
        category_id = self.kwargs.get('category_id')
        category = get_object_or_404(Category, id=category_id)
        context['active_category'] = category.id
        context['active_category_name'] = category.name
        return context
